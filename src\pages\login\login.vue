<template>
  <view class="login-container">
    <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
    
    <view class="login-content">
      <view class="logo-section">
        <view class="logo">🎲</view>
        <text class="app-title">易道数</text>
      </view>

      <view class="form-section">
        <view class="input-group">
          <input 
            class="password-input"
            type="password"
            v-model="password"
            placeholder="请输入密码"
            @confirm="handleLogin"
            :focus="true"
          />
        </view>
        
        <button class="login-btn" @click="handleLogin" :disabled="!password">
          登录
        </button>
      </view>
    </view>
  </view>
</template>

<script>
import { AuthUtil } from '@/utils/auth.js'

export default {
  data() {
    return {
      password: '',
      statusBarHeight: 0
    }
  },
  
  onLoad() {
    // 初始化密码
    AuthUtil.initPassword()
    
    // 获取状态栏高度
    const systemInfo = uni.getSystemInfoSync()
    this.statusBarHeight = systemInfo.statusBarHeight || 0
    
    // 如果已经登录，直接跳转到主页
    if (AuthUtil.checkLoginStatus()) {
      this.navigateToHome()
    }
  },
  
  methods: {
    handleLogin() {
      if (!this.password) {
        uni.showToast({
          title: '请输入密码',
          icon: 'none'
        })
        return
      }
      
      if (AuthUtil.login(this.password)) {
        uni.showToast({
          title: '登录成功',
          icon: 'success'
        })
        
        setTimeout(() => {
          this.navigateToHome()
        }, 1000)
      } else {
        uni.showToast({
          title: '密码错误',
          icon: 'none'
        })
        this.password = ''
      }
    },
    
    navigateToHome() {
      uni.reLaunch({
        url: '/pages/home/<USER>'
      })
    }
  }
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #FF6B6B, #FF8E8E);
  display: flex;
  flex-direction: column;
}

.status-bar {
  background: transparent;
}

.login-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 0 60rpx;
}

.logo-section {
  text-align: center;
  margin-bottom: 180rpx;
}

.logo {
  font-size: 120rpx;
  margin-bottom: 40rpx;
}

.app-title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 20rpx;
}

.app-subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.form-section {
  width: 100%;
}

.input-group {
  margin-bottom: 40rpx;
}

.password-input {
  width: 100%;
  height: 100rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50rpx;
  padding: 0 40rpx;
  font-size: 64rpx;
  box-sizing: border-box;
}

.login-btn {
  width: 100%;
  height: 100rpx;
  background: #FF0000;
  color: white;
  border: none;
  border-radius: 50rpx;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 40rpx;
}

.login-btn:disabled {
  background: #ccc;
}
</style>
