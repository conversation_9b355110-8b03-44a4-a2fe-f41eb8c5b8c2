# 易道数历法功能模块设计文档

## 项目概述

为"易道数"应用添加中华万年历功能模块，集成公历、农历、佛历、道历等多种历法系统，并提供定时提醒功能。基于现有Vue3 + uni-app架构，使用6tail/lunar-javascript库实现核心历法计算。

## 技术栈

- **核心框架**: Vue 3.4.21 + uni-app 3.0.0
- **历法库**: lunar-javascript 1.7.3
- **构建工具**: Vite 5.2.8
- **UI组件**: @dcloudio/uni-ui
- **通知API**: uni-app本地通知

## 功能模块设计

### 1. 中华万年历模块

#### 1.1 核心功能
- **公农历对照**: 显示公历与农历日期对应关系
- **二十四节气**: 自动标注节气信息及具体时间
- **天干地支**: 显示年月日时的干支纪法
- **生肖年份**: 显示当前年份对应的生肖
- **黄历宜忌**: 提供每日宜忌事项（如宜出行、忌动土）
- **吉神方位**: 显示喜神、财神、福神等方位信息
- **星宿信息**: 显示二十八星宿当值信息

#### 1.2 界面设计
```
┌─────────────────────────────┐
│        中华万年历            │
├─────────────────────────────┤
│  2024年1月15日 星期一        │
│  农历癸卯年腊月初五          │
│  甲辰年 丁丑月 壬寅日        │
├─────────────────────────────┤
│  【宜】祈福 出行 嫁娶        │
│  【忌】动土 开仓 安葬        │
├─────────────────────────────┤
│  喜神: 正南  财神: 正东      │
│  星宿: 角木蛟(吉)           │
└─────────────────────────────┘
```

### 2. 佛历集成模块

#### 2.1 核心功能
- **佛教历法**: 显示佛历年月日
- **重要节日**: 标注佛诞日、观音诞、地藏诞等
- **斋戒日**: 显示十斋日、六斋日信息
- **修行提醒**: 每日佛教修行建议

#### 2.2 重要佛教节日
```javascript
const buddhistFestivals = {
  '农历四月初八': '佛诞日(释迦牟尼佛诞)',
  '农历二月十九': '观音诞',
  '农历六月十九': '观音成道日',
  '农历九月十九': '观音出家日',
  '农历七月三十': '地藏诞',
  '农历正月初一': '弥勒佛诞'
}
```

### 3. 道历集成模块

#### 3.1 核心功能
- **道教历法**: 显示道历纪年
- **重要节日**: 标注三清诞辰、中元节等
- **修炼吉日**: 显示适合修炼的日期
- **养生时辰**: 提供道教养生时间建议

#### 3.2 重要道教节日
```javascript
const taoistFestivals = {
  '农历正月初九': '玉皇大帝诞',
  '农历二月十五': '太上老君诞',
  '农历七月十五': '中元节',
  '农历九月初九': '重阳节',
  '农历十二月二十三': '灶王爷诞'
}
```

### 4. 定时提醒功能

#### 4.1 提醒类型
- **单次提醒**: 指定具体日期时间
- **重复提醒**: 每月农历初一、十五等
- **节日提醒**: 重要传统节日自动提醒
- **修行提醒**: 佛道修行日提醒

#### 4.2 提醒管理
- **添加提醒**: 选择日期、时间、内容
- **编辑提醒**: 修改已设置的提醒
- **删除提醒**: 移除不需要的提醒
- **提醒历史**: 查看已触发的提醒记录

## 技术实现方案

### 1. 依赖安装

```bash
npm install lunar-javascript
```

### 2. 工具类设计

#### 2.1 历法工具类 (utils/calendar.js)
```javascript
import { Solar, Lunar } from 'lunar-javascript'

export class CalendarUtil {
  // 获取指定日期的完整历法信息
  static getCalendarInfo(date = new Date()) {
    const solar = Solar.fromDate(date)
    const lunar = solar.getLunar()
    
    return {
      solar: {
        year: solar.getYear(),
        month: solar.getMonth(),
        day: solar.getDay(),
        weekday: solar.getWeek()
      },
      lunar: {
        year: lunar.getYear(),
        month: lunar.getMonth(),
        day: lunar.getDay(),
        yearInChinese: lunar.getYearInChinese(),
        monthInChinese: lunar.getMonthInChinese(),
        dayInChinese: lunar.getDayInChinese()
      },
      ganzhi: {
        year: lunar.getYearInGanZhi(),
        month: lunar.getMonthInGanZhi(),
        day: lunar.getDayInGanZhi()
      },
      zodiac: lunar.getYearShengXiao(),
      jieqi: solar.getJieQi(),
      yi: lunar.getDayYi(),
      ji: lunar.getDayJi(),
      shenwei: {
        xi: lunar.getDayPositionXi(),
        cai: lunar.getDayPositionCai(),
        fu: lunar.getDayPositionFu()
      }
    }
  }
}
```

#### 2.2 提醒工具类 (utils/reminder.js)
```javascript
export class ReminderUtil {
  // 设置本地通知
  static setLocalNotification(options) {
    return uni.createLocalNotification({
      title: options.title,
      content: options.content,
      when: options.when,
      sound: 'system'
    })
  }
  
  // 保存提醒设置
  static saveReminder(reminder) {
    const reminders = StorageUtil.getItem('calendar_reminders', [])
    reminders.push({
      id: RandomUtil.generateId(),
      ...reminder,
      createTime: Date.now()
    })
    StorageUtil.setItem('calendar_reminders', reminders)
  }
}
```

### 3. 页面结构设计

#### 3.1 新增页面
```
src/pages/
├── calendar/              # 历法模块
│   ├── calendar.vue      # 万年历主页
│   ├── lunar-detail.vue  # 农历详情页
│   ├── buddhist.vue      # 佛历页面
│   ├── taoist.vue        # 道历页面
│   └── reminders.vue     # 提醒管理页
```

#### 3.2 pages.json配置
```json
{
  "path": "pages/calendar/calendar",
  "style": {
    "navigationBarTitleText": "中华万年历",
    "navigationBarBackgroundColor": "#FF0000"
  }
},
{
  "path": "pages/calendar/reminders",
  "style": {
    "navigationBarTitleText": "定时提醒",
    "navigationBarBackgroundColor": "#FF0000"
  }
}
```

### 4. 主页面集成

#### 4.1 home.vue修改
在主页面添加历法模块入口：
```vue
<view class="mode-item" @click="navigateToCalendar">
  <view class="mode-icon">
    <image src="/src/static/calendar.png" class="mode-icon-image"></image>
  </view>
  <view class="mode-info">
    <text class="mode-title">中华万年历</text>
  </view>
  <view class="arrow">></view>
</view>
```

### 5. 存储扩展

#### 5.1 storage.js扩展
```javascript
// 历法相关存储方法
static getCalendarSettings() {
  return this.getItem('calendar_settings', {
    showLunar: true,
    showBuddhist: false,
    showTaoist: false,
    reminderEnabled: true
  })
}

static saveCalendarSettings(settings) {
  this.setItem('calendar_settings', settings)
}

static getReminders() {
  return this.getItem('calendar_reminders', [])
}
```

## UI设计规范

### 1. 色彩方案
- **主色调**: #FF0000 (与现有应用保持一致)
- **辅助色**: #F5F5F5 (背景色)
- **文字色**: #333 (主要文字), #666 (次要文字)
- **强调色**: #FFD700 (重要节日), #FF6B6B (忌日)

### 2. 组件设计
- **日历网格**: 7x6网格布局，支持滑动切换月份
- **信息卡片**: 圆角卡片设计，阴影效果
- **按钮样式**: 与现有模式按钮保持一致
- **图标设计**: 简洁线性图标风格

### 3. 响应式适配
- **小屏设备**: 优化触摸操作，合理间距
- **大屏设备**: 充分利用屏幕空间
- **横屏适配**: 支持横屏显示

## 开发计划

### 阶段一：基础历法功能 (1-2周)
1. 安装lunar-javascript依赖
2. 创建CalendarUtil工具类
3. 开发万年历主页面
4. 实现公农历对照显示

### 阶段二：扩展功能 (1-2周)
1. 集成佛历、道历显示
2. 添加节日标注功能
3. 实现黄历宜忌显示
4. 优化UI交互体验

### 阶段三：提醒功能 (1周)
1. 开发提醒管理页面
2. 实现本地通知功能
3. 添加提醒设置界面
4. 测试跨平台兼容性

### 阶段四：优化完善 (1周)
1. 性能优化
2. 错误处理完善
3. 用户体验优化
4. 文档完善

## 注意事项

1. **版权合规**: 使用开源lunar-javascript库，避免侵权
2. **性能优化**: 历法计算较复杂，需要合理缓存
3. **跨平台兼容**: 确保H5、小程序、APP平台功能一致
4. **数据准确性**: 历法数据需要严格验证
5. **用户体验**: 保持与现有应用风格一致

## 详细技术实现

### 1. 万年历主页面实现 (calendar.vue)

```vue
<template>
  <view class="calendar-container">
    <!-- 日期导航 -->
    <view class="date-nav">
      <button @click="prevMonth">‹</button>
      <text class="current-date">{{ currentYearMonth }}</text>
      <button @click="nextMonth">›</button>
    </view>

    <!-- 今日信息卡片 -->
    <view class="today-info">
      <view class="solar-date">{{ todayInfo.solar.year }}年{{ todayInfo.solar.month }}月{{ todayInfo.solar.day }}日</view>
      <view class="lunar-date">{{ todayInfo.lunar.yearInChinese }}年{{ todayInfo.lunar.monthInChinese }}{{ todayInfo.lunar.dayInChinese }}</view>
      <view class="ganzhi">{{ todayInfo.ganzhi.year }}年 {{ todayInfo.ganzhi.month }}月 {{ todayInfo.ganzhi.day }}日</view>
    </view>

    <!-- 黄历信息 -->
    <view class="huangli-info">
      <view class="yi-ji">
        <text class="label">宜：</text>
        <text class="content">{{ todayInfo.yi.join(' ') }}</text>
      </view>
      <view class="yi-ji">
        <text class="label">忌：</text>
        <text class="content">{{ todayInfo.ji.join(' ') }}</text>
      </view>
    </view>

    <!-- 日历网格 -->
    <view class="calendar-grid">
      <view class="weekdays">
        <text v-for="day in weekdays" :key="day" class="weekday">{{ day }}</text>
      </view>
      <view class="days">
        <view
          v-for="day in calendarDays"
          :key="day.key"
          :class="['day-cell', { 'today': day.isToday, 'other-month': day.isOtherMonth }]"
          @click="selectDate(day)"
        >
          <text class="solar-day">{{ day.solar }}</text>
          <text class="lunar-day">{{ day.lunar }}</text>
          <view v-if="day.festival" class="festival">{{ day.festival }}</view>
        </view>
      </view>
    </view>

    <!-- 功能按钮 -->
    <view class="function-buttons">
      <button @click="goToBuddhist">佛历</button>
      <button @click="goToTaoist">道历</button>
      <button @click="goToReminders">提醒</button>
    </view>
  </view>
</template>

<script>
import { CalendarUtil } from '@/utils/calendar.js'
import { AuthUtil } from '@/utils/auth.js'

export default {
  data() {
    return {
      currentDate: new Date(),
      todayInfo: {},
      calendarDays: [],
      weekdays: ['日', '一', '二', '三', '四', '五', '六']
    }
  },

  computed: {
    currentYearMonth() {
      return `${this.currentDate.getFullYear()}年${this.currentDate.getMonth() + 1}月`
    }
  },

  onLoad() {
    AuthUtil.requireAuth()
    this.initCalendar()
  },

  methods: {
    initCalendar() {
      this.todayInfo = CalendarUtil.getCalendarInfo(new Date())
      this.generateCalendarDays()
    },

    generateCalendarDays() {
      // 生成日历网格数据
      this.calendarDays = CalendarUtil.generateMonthDays(this.currentDate)
    },

    prevMonth() {
      this.currentDate.setMonth(this.currentDate.getMonth() - 1)
      this.generateCalendarDays()
    },

    nextMonth() {
      this.currentDate.setMonth(this.currentDate.getMonth() + 1)
      this.generateCalendarDays()
    }
  }
}
</script>
```

### 2. 扩展的历法工具类

```javascript
// utils/calendar.js
import { Solar, Lunar } from 'lunar-javascript'
import { StorageUtil } from './storage.js'

export class CalendarUtil {
  // 佛教节日数据
  static BUDDHIST_FESTIVALS = {
    '农历四月初八': '佛诞日',
    '农历二月十九': '观音诞',
    '农历六月十九': '观音成道日',
    '农历九月十九': '观音出家日',
    '农历七月三十': '地藏诞'
  }

  // 道教节日数据
  static TAOIST_FESTIVALS = {
    '农历正月初九': '玉皇大帝诞',
    '农历二月十五': '太上老君诞',
    '农历七月十五': '中元节',
    '农历九月初九': '重阳节'
  }

  // 生成月份日历数据
  static generateMonthDays(date) {
    const year = date.getFullYear()
    const month = date.getMonth()
    const firstDay = new Date(year, month, 1)
    const lastDay = new Date(year, month + 1, 0)
    const startDate = new Date(firstDay)
    startDate.setDate(startDate.getDate() - firstDay.getDay())

    const days = []
    const today = new Date()

    for (let i = 0; i < 42; i++) {
      const currentDate = new Date(startDate)
      currentDate.setDate(startDate.getDate() + i)

      const solar = Solar.fromDate(currentDate)
      const lunar = solar.getLunar()

      days.push({
        key: `${currentDate.getFullYear()}-${currentDate.getMonth()}-${currentDate.getDate()}`,
        date: new Date(currentDate),
        solar: currentDate.getDate(),
        lunar: lunar.getDayInChinese(),
        isToday: this.isSameDay(currentDate, today),
        isOtherMonth: currentDate.getMonth() !== month,
        festival: this.getFestival(lunar),
        jieqi: solar.getJieQi()
      })
    }

    return days
  }

  // 获取节日信息
  static getFestival(lunar) {
    const lunarDate = `农历${lunar.getMonthInChinese()}${lunar.getDayInChinese()}`
    return this.BUDDHIST_FESTIVALS[lunarDate] || this.TAOIST_FESTIVALS[lunarDate] || null
  }

  // 判断是否同一天
  static isSameDay(date1, date2) {
    return date1.getFullYear() === date2.getFullYear() &&
           date1.getMonth() === date2.getMonth() &&
           date1.getDate() === date2.getDate()
  }

  // 获取佛历信息
  static getBuddhistInfo(date = new Date()) {
    const solar = Solar.fromDate(date)
    const lunar = solar.getLunar()

    // 佛历计算（以释迦牟尼佛涅槃后计算）
    const buddhistYear = solar.getYear() + 543

    return {
      year: buddhistYear,
      festivals: this.getBuddhistFestivals(lunar),
      zhaiDays: this.getZhaiDays(lunar),
      practice: this.getDailyPractice(lunar)
    }
  }

  // 获取道历信息
  static getTaoistInfo(date = new Date()) {
    const solar = Solar.fromDate(date)
    const lunar = solar.getLunar()

    return {
      festivals: this.getTaoistFestivals(lunar),
      luckyDays: this.getTaoistLuckyDays(lunar),
      yangsheng: this.getYangshengAdvice(solar)
    }
  }

  // 获取斋戒日信息
  static getZhaiDays(lunar) {
    const day = lunar.getDay()
    const zhaiDays = [1, 8, 14, 15, 18, 23, 24, 28, 29, 30]
    return zhaiDays.includes(day) ? `今日是斋戒日` : null
  }

  // 获取每日修行建议
  static getDailyPractice(lunar) {
    const practices = [
      '宜念佛诵经，修心养性',
      '宜静坐冥想，观照内心',
      '宜行善布施，积累功德',
      '宜持戒修行，净化身心'
    ]
    return practices[lunar.getDay() % practices.length]
  }
}
```

### 3. 提醒功能实现

```javascript
// utils/reminder.js
export class ReminderUtil {
  // 提醒类型枚举
  static REMINDER_TYPES = {
    ONCE: 'once',           // 单次提醒
    LUNAR_MONTHLY: 'lunar_monthly',  // 农历月重复
    SOLAR_MONTHLY: 'solar_monthly',  // 公历月重复
    FESTIVAL: 'festival'    // 节日提醒
  }

  // 创建提醒
  static createReminder(options) {
    const reminder = {
      id: this.generateId(),
      title: options.title,
      content: options.content,
      type: options.type,
      date: options.date,
      time: options.time,
      enabled: true,
      createTime: Date.now()
    }

    this.saveReminder(reminder)
    this.scheduleNotification(reminder)
    return reminder
  }

  // 安排通知
  static scheduleNotification(reminder) {
    const notificationTime = this.calculateNotificationTime(reminder)

    if (notificationTime > Date.now()) {
      uni.createLocalNotification({
        id: reminder.id,
        title: reminder.title,
        content: reminder.content,
        when: notificationTime,
        sound: 'system'
      })
    }
  }

  // 计算通知时间
  static calculateNotificationTime(reminder) {
    const [hours, minutes] = reminder.time.split(':').map(Number)
    let targetDate = new Date(reminder.date)
    targetDate.setHours(hours, minutes, 0, 0)

    // 如果是重复提醒，计算下次提醒时间
    if (reminder.type === this.REMINDER_TYPES.LUNAR_MONTHLY) {
      // 农历月重复逻辑
      targetDate = this.getNextLunarDate(reminder.date, targetDate)
    } else if (reminder.type === this.REMINDER_TYPES.SOLAR_MONTHLY) {
      // 公历月重复逻辑
      while (targetDate <= new Date()) {
        targetDate.setMonth(targetDate.getMonth() + 1)
      }
    }

    return targetDate.getTime()
  }

  // 获取下一个农历日期
  static getNextLunarDate(originalDate, currentTime) {
    // 使用lunar-javascript计算下一个相同农历日期
    const solar = Solar.fromDate(new Date(originalDate))
    const lunar = solar.getLunar()
    const targetLunarDay = lunar.getDay()

    let nextDate = new Date(currentTime)
    nextDate.setMonth(nextDate.getMonth() + 1)

    // 查找下个月对应的农历日期
    for (let i = 0; i < 35; i++) {
      const testDate = new Date(nextDate)
      testDate.setDate(nextDate.getDate() + i)
      const testSolar = Solar.fromDate(testDate)
      const testLunar = testSolar.getLunar()

      if (testLunar.getDay() === targetLunarDay) {
        return testDate
      }
    }

    return nextDate
  }
}
```

### 4. 存储扩展实现

```javascript
// 在storage.js中添加历法相关方法
export class StorageUtil {
  // ... 现有方法 ...

  // 历法设置相关
  static getCalendarSettings() {
    return this.getItem('calendar_settings', {
      showLunar: true,
      showBuddhist: true,
      showTaoist: true,
      reminderEnabled: true,
      defaultView: 'calendar' // calendar, buddhist, taoist
    })
  }

  static saveCalendarSettings(settings) {
    this.setItem('calendar_settings', settings)
  }

  // 提醒相关
  static getReminders() {
    return this.getItem('calendar_reminders', [])
  }

  static saveReminder(reminder) {
    const reminders = this.getReminders()
    const existingIndex = reminders.findIndex(r => r.id === reminder.id)

    if (existingIndex >= 0) {
      reminders[existingIndex] = reminder
    } else {
      reminders.push(reminder)
    }

    this.setItem('calendar_reminders', reminders)
  }

  static deleteReminder(reminderId) {
    const reminders = this.getReminders()
    const filteredReminders = reminders.filter(r => r.id !== reminderId)
    this.setItem('calendar_reminders', filteredReminders)

    // 取消对应的本地通知
    uni.cancelLocalNotification({ id: reminderId })
  }

  // 历法缓存相关
  static getCachedCalendarData(dateKey) {
    const cache = this.getItem('calendar_cache', {})
    return cache[dateKey] || null
  }

  static setCachedCalendarData(dateKey, data) {
    const cache = this.getItem('calendar_cache', {})
    cache[dateKey] = {
      data: data,
      timestamp: Date.now()
    }

    // 清理过期缓存（保留30天）
    const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000)
    Object.keys(cache).forEach(key => {
      if (cache[key].timestamp < thirtyDaysAgo) {
        delete cache[key]
      }
    })

    this.setItem('calendar_cache', cache)
  }
}
```

## 预期效果

通过添加历法功能模块，"易道数"应用将成为一个集随机数生成、传统历法查询、定时提醒于一体的综合性易学工具，满足用户对传统文化和现代便利性的双重需求。

### 用户价值
1. **传统文化传承**: 让用户了解和使用传统历法
2. **实用工具集成**: 一个应用满足多种需求
3. **个性化提醒**: 根据传统节日和个人需求设置提醒
4. **跨平台体验**: 在不同设备上保持一致的使用体验

### 技术价值
1. **模块化设计**: 便于后续功能扩展
2. **性能优化**: 通过缓存机制提升响应速度
3. **数据准确性**: 基于成熟的lunar-javascript库
4. **用户体验**: 保持与现有应用的一致性
