<template>
  <view class="mode-container">
 

    <view class="content">
      <!-- 结果显示区域 -->
      <view class="result-section" v-show="showResult && !isInputFocused">
        <view class="result-number" @click="copyResult">{{ currentResult }}</view>
        <view class="result-info">{{ selectedDigits }}位数</view>
        <view class="result-text">{{ currentText }}</view>
        <view class="result-time">{{ currentTime }}</view>
      </view>

      <!-- 位数选择区域 -->
      <view class="digits-section">
        
        <view class="digits-grid">
          <view 
            class="digit-item"
            :class="{ active: selectedDigits === digit }"
            v-for="digit in digitOptions"
            :key="digit"
            @click="selectDigits(digit)"
          >
            {{ digit }}位
          </view>
        </view>
      </view>

      <!-- 输入区域 -->
      <view class="input-section">
        
        <textarea 
          class="text-input"
          v-model="inputText"
          placeholder="请输入文字（最多50个字）"
          maxlength="50"
          :show-confirm-bar="false"
          @focus="handleInputFocus"
          @blur="handleInputBlur"
        />
        <view class="char-count">{{ inputText.length }}/50</view>
      </view>

      <!-- 确定按钮 -->
      <view class="button-section">
        <button class="confirm-btn" @click="generateNumber" :disabled="!inputText.trim() || !selectedDigits">
          确定
        </button>
      </view>

      <!-- 历史记录按钮 -->
      <view class="history-section">
        <button class="history-btn" @click="viewHistory">
          查看自由组数记录
        </button>
      </view>
    </view>
  </view>
</template>

<script>
import { AuthUtil } from '@/utils/auth.js'
import { RandomUtil } from '@/utils/random.js'
import { StorageUtil } from '@/utils/storage.js'

export default {
  data() {
    return {
      inputText: '',
      selectedDigits: null,
      digitOptions: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13],
      showResult: false,
      currentResult: '',
      currentText: '',
      currentTime: '',
      isInputFocused: false
    }
  },

  onLoad() {
    AuthUtil.requireAuth()
  },

  methods: {
    selectDigits(digits) {
      this.selectedDigits = digits
    },

    generateNumber() {
      if (!this.inputText.trim()) {
        uni.showToast({
          title: '请输入文字',
          icon: 'none'
        })
        return
      }

      if (!this.selectedDigits) {
        uni.showToast({
          title: '请选择位数',
          icon: 'none'
        })
        return
      }

      try {
        // 显示加载提示


        // 延时1.5秒后生成随机数
        setTimeout(() => {
          try {
            // 生成随机数
            const result = RandomUtil.generateMode3(this.selectedDigits)
            const timestamp = RandomUtil.getCurrentTimestamp()
            
            // 显示结果
            this.currentResult = result
            this.currentText = this.inputText.trim()
            this.currentTime = timestamp
            this.showResult = true

            // 保存记录
            const record = RandomUtil.createRecord('mode3', this.currentText, `${result} (${this.selectedDigits}位)`)
            StorageUtil.saveRecord(record)

            // 清空输入
            this.inputText = ''

            // 隐藏加载提示并显示成功提示
            uni.hideLoading()

          } catch (error) {
            uni.hideLoading()
            uni.showToast({
              title: error.message,
              icon: 'none'
            })
          }
        }, 1500)
      } catch (error) {
        uni.showToast({
          title: error.message,
          icon: 'none'
        })
      }
    },

    viewHistory() {
      uni.navigateTo({
        url: '/pages/mode-records/mode-records?mode=mode3&title=自由组数记录'
      })
    },

    handleInputFocus() {
      this.isInputFocused = true
    },

    handleInputBlur() {
      this.isInputFocused = false
    },

    // 复制结果到剪贴板
    copyResult() {
      if (this.currentText) {
        uni.setClipboardData({
          data: this.currentText.toString(),
          fail: () => {
            uni.showToast({
              title: '复制失败',
              icon: 'none',
              duration: 1500
            })
          }
        })
      }
    }
  }
}
</script>

<style scoped>
.mode-container {
  min-height: 100vh;
  background: #F5F5F5;
  padding: 40rpx;
}

.header {
  text-align: center;
  margin-bottom: 60rpx;
}

.mode-title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.mode-desc {
  display: block;
  font-size: 28rpx;
  color: #666;
}

.content {
  max-width: 600rpx;
  margin: 0 auto;
}

.result-section {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx 40rpx 30rpx 40rpx;
  text-align: center;
  margin-bottom: 60rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.result-number {
  font-size: 160rpx;
  font-weight: bold;
  color: #FF0000;
  margin-bottom: 20rpx;
  margin-top: 40rpx;
  word-break: break-all;
  line-height: 1.2;
  cursor: pointer;
  user-select: none;
  transition: opacity 0.2s ease;
}

.result-number:active {
  opacity: 0.7;
}

.result-info {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.result-text {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 20rpx;
  word-break: break-all;
}

.result-time {
  font-size: 24rpx;
  color: #999;
}

.digits-section {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.step-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.digits-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.digit-item {
  flex: 0 0 calc(20% - 16rpx);
  height: 80rpx;
  background: #F8F8F8;
  border-radius: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  color: #666;
  transition: all 0.3s ease;
}

.digit-item.active {
  background: #FF0000;
  color: white;
}

.input-section {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.text-input {
  width: 100%;
  min-height: 120rpx;
  background: #F8F8F8;
  border-radius: 10rpx;
  padding: 20rpx;
  font-size: 40rpx;
  box-sizing: border-box;
  margin-bottom: 10rpx;
}

.char-count {
  text-align: right;
  font-size: 24rpx;
  color: #999;
}

.button-section {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.confirm-btn {
  width: 100%;
  height: 100rpx;
  background: #FF0000;
  color: white;
  border: none;
  border-radius: 50rpx;
  font-size: 36rpx;
  font-weight: bold;
}

.confirm-btn:disabled {
  background: #ccc;
}

.history-section {
  text-align: center;
}

.history-btn {
  width: 100%;
  height: 80rpx;
  background: transparent;
  color: #FF0000;
  border: 2rpx solid #FF0000;
  border-radius: 40rpx;
  font-size: 30rpx;
}
</style>
