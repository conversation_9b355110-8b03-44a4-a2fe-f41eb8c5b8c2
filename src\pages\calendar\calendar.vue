<template>
  <view class="calendar-container">
    <!-- 头部标题 -->
    <view class="header">
      <text class="title">中华万年历</text>
      <text class="subtitle">传统历法 · 文化传承</text>
    </view>

    <!-- 今日信息卡片 -->
    <view class="today-card">
      <view class="date-section">
        <view class="solar-date">
          <text class="year">{{ todayInfo.solar?.year }}年</text>
          <text class="month-day">{{ todayInfo.solar?.month }}月{{ todayInfo.solar?.day }}日</text>
          <text class="weekday">{{ getWeekdayText(todayInfo.solar?.weekday) }}</text>
        </view>
        <view class="lunar-date">
          <text>{{ todayInfo.lunar?.yearInChinese }}年{{ todayInfo.lunar?.monthInChinese }}{{ todayInfo.lunar?.dayInChinese }}</text>
        </view>
      </view>
      
      <view class="info-section">
        <view class="info-row">
          <text class="label">干支：</text>
          <text class="value">{{ todayInfo.ganzhi?.year }}年 {{ todayInfo.ganzhi?.month }}月 {{ todayInfo.ganzhi?.day }}日</text>
        </view>
        <view class="info-row">
          <text class="label">生肖：</text>
          <text class="value">{{ todayInfo.zodiac }} · {{ todayInfo.solar?.constellation }}座</text>
        </view>
        <view class="info-row" v-if="todayInfo.jieqi">
          <text class="label">节气：</text>
          <text class="value jieqi">{{ todayInfo.jieqi }}</text>
        </view>
        <view class="info-row" v-if="festivalText">
          <text class="label">节日：</text>
          <text class="value festival">{{ festivalText }}</text>
        </view>
      </view>
    </view>

    <!-- 黄历宜忌卡片 -->
    <view class="huangli-card">
      <view class="card-title">
        <text>黄历宜忌</text>
      </view>
      <view class="yi-ji-section">
        <view class="yi-section">
          <view class="yi-ji-header">
            <text class="yi-label">宜</text>
          </view>
          <view class="yi-ji-content">
            <text class="yi-text">{{ yiText }}</text>
          </view>
        </view>
        <view class="ji-section">
          <view class="yi-ji-header">
            <text class="ji-label">忌</text>
          </view>
          <view class="yi-ji-content">
            <text class="ji-text">{{ jiText }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 神位方位卡片 -->
    <view class="shenwei-card">
      <view class="card-title">
        <text>神位方位</text>
      </view>
      <view class="shenwei-grid">
        <view class="shenwei-item">
          <text class="shenwei-label">喜神</text>
          <text class="shenwei-value">{{ todayInfo.shenwei?.xi }}</text>
        </view>
        <view class="shenwei-item">
          <text class="shenwei-label">财神</text>
          <text class="shenwei-value">{{ todayInfo.shenwei?.cai }}</text>
        </view>
        <view class="shenwei-item">
          <text class="shenwei-label">福神</text>
          <text class="shenwei-value">{{ todayInfo.shenwei?.fu }}</text>
        </view>
      </view>
    </view>

    <!-- 功能按钮区域 -->
    <view class="function-buttons">
      <button class="function-btn" @click="goToBuddhist">
        <view class="btn-content">
          <text class="btn-icon">🙏</text>
          <text class="btn-text">佛历</text>
        </view>
      </button>
      <button class="function-btn" @click="goToTaoist">
        <view class="btn-content">
          <text class="btn-icon">☯</text>
          <text class="btn-text">道历</text>
        </view>
      </button>
      <button class="function-btn" @click="goToReminders">
        <view class="btn-content">
          <text class="btn-icon">⏰</text>
          <text class="btn-text">提醒</text>
        </view>
      </button>
    </view>

    <!-- 加载状态 -->
    <view v-if="loading" class="loading-overlay">
      <text class="loading-text">正在加载历法信息...</text>
    </view>
  </view>
</template>

<script>
import { CalendarUtil } from '@/utils/calendar.js'
import { StorageUtil } from '@/utils/storage.js'
import { AuthUtil } from '@/utils/auth.js'

export default {
  data() {
    return {
      todayInfo: {},
      loading: true
    }
  },
  
  computed: {
    // 宜事项文本
    yiText() {
      if (!this.todayInfo.yi || this.todayInfo.yi.length === 0) {
        return '诸事不宜'
      }
      return this.todayInfo.yi.slice(0, 6).join(' ')
    },
    
    // 忌事项文本
    jiText() {
      if (!this.todayInfo.ji || this.todayInfo.ji.length === 0) {
        return '百无禁忌'
      }
      return this.todayInfo.ji.slice(0, 6).join(' ')
    },
    
    // 节日文本
    festivalText() {
      if (!this.todayInfo.festivals || this.todayInfo.festivals.length === 0) {
        return ''
      }
      return this.todayInfo.festivals.map(f => f.name).join(' ')
    }
  },
  
  onLoad() {
    // 检查登录状态
    AuthUtil.requireAuth()
    this.loadTodayInfo()
  },
  
  onShow() {
    // 每次显示页面时刷新数据
    this.loadTodayInfo()
  },
  
  methods: {
    // 加载今日历法信息
    async loadTodayInfo() {
      try {
        this.loading = true
        
        // 尝试从缓存获取数据
        const today = new Date()
        const dateKey = `${today.getFullYear()}-${today.getMonth() + 1}-${today.getDate()}`
        let cachedData = StorageUtil.getCachedCalendarData(dateKey)
        
        if (cachedData) {
          this.todayInfo = cachedData
          this.loading = false
        } else {
          // 获取新数据
          const calendarInfo = CalendarUtil.getCalendarInfo(today)
          if (calendarInfo) {
            this.todayInfo = calendarInfo
            // 缓存数据
            StorageUtil.setCachedCalendarData(dateKey, calendarInfo)
            // 保存查看记录
            StorageUtil.saveCalendarViewRecord(today, 'calendar')
          } else {
            throw new Error('获取历法信息失败')
          }
        }
        
        console.log('今日历法信息:', this.todayInfo)
        
      } catch (error) {
        console.error('加载历法信息失败:', error)
        uni.showToast({
          title: '加载失败，请重试',
          icon: 'none',
          duration: 2000
        })
      } finally {
        this.loading = false
      }
    },
    
    // 获取星期文本
    getWeekdayText(weekday) {
      const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
      return weekdays[weekday - 1] || ''
    },
    
    // 跳转到佛历页面
    goToBuddhist() {
      uni.navigateTo({
        url: '/pages/calendar/buddhist'
      })
    },

    // 跳转到道历页面
    goToTaoist() {
      uni.navigateTo({
        url: '/pages/calendar/taoist'
      })
    },
    
    // 跳转到提醒页面
    goToReminders() {
      uni.navigateTo({
        url: '/pages/calendar/reminders'
      })
    },
    
    // 复制今日信息
    copyTodayInfo() {
      if (!this.todayInfo.solar) return
      
      const info = `${this.todayInfo.solar.year}年${this.todayInfo.solar.month}月${this.todayInfo.solar.day}日 ${this.getWeekdayText(this.todayInfo.solar.weekday)}
农历${this.todayInfo.lunar.yearInChinese}年${this.todayInfo.lunar.monthInChinese}${this.todayInfo.lunar.dayInChinese}
干支：${this.todayInfo.ganzhi.year}年 ${this.todayInfo.ganzhi.month}月 ${this.todayInfo.ganzhi.day}日
生肖：${this.todayInfo.zodiac} · ${this.todayInfo.solar.constellation}座
宜：${this.yiText}
忌：${this.jiText}`
      
      uni.setClipboardData({
        data: info,
        success: () => {
          uni.showToast({
            title: '已复制到剪贴板',
            icon: 'success'
          })
        },
        fail: () => {
          uni.showToast({
            title: '复制失败',
            icon: 'none'
          })
        }
      })
    }
  }
}
</script>

<style scoped>
.calendar-container {
  min-height: 100vh;
  background: #F5F5F5;
  padding: 40rpx;
}

/* 头部样式 */
.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.subtitle {
  display: block;
  font-size: 24rpx;
  color: #666;
}

/* 卡片通用样式 */
.today-card, .huangli-card, .shenwei-card {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

/* 今日信息卡片 */
.date-section {
  border-bottom: 2rpx solid #F0F0F0;
  padding-bottom: 30rpx;
  margin-bottom: 30rpx;
}

.solar-date {
  display: flex;
  align-items: baseline;
  margin-bottom: 15rpx;
}

.year {
  font-size: 28rpx;
  color: #666;
  margin-right: 10rpx;
}

.month-day {
  font-size: 42rpx;
  font-weight: bold;
  color: #FF0000;
  margin-right: 15rpx;
}

.weekday {
  font-size: 24rpx;
  color: #888;
}

.lunar-date {
  font-size: 28rpx;
  color: #666;
}

.info-section {
  
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}

.label {
  font-size: 26rpx;
  color: #888;
  width: 120rpx;
  flex-shrink: 0;
}

.value {
  font-size: 26rpx;
  color: #333;
  flex: 1;
}

.jieqi {
  color: #FF6B6B;
  font-weight: bold;
}

.festival {
  color: #FFD700;
  font-weight: bold;
}

/* 黄历宜忌卡片 */
.card-title {
  text-align: center;
  margin-bottom: 30rpx;
}

.card-title text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.yi-ji-section {
  
}

.yi-section, .ji-section {
  margin-bottom: 25rpx;
}

.ji-section {
  margin-bottom: 0;
}

.yi-ji-header {
  margin-bottom: 15rpx;
}

.yi-label {
  display: inline-block;
  background: #4CAF50;
  color: white;
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-weight: bold;
}

.ji-label {
  display: inline-block;
  background: #FF5722;
  color: white;
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-weight: bold;
}

.yi-ji-content {
  
}

.yi-text, .ji-text {
  font-size: 26rpx;
  color: #333;
  line-height: 1.6;
}

/* 神位方位卡片 */
.shenwei-grid {
  display: flex;
  justify-content: space-between;
}

.shenwei-item {
  text-align: center;
  flex: 1;
}

.shenwei-label {
  display: block;
  font-size: 24rpx;
  color: #888;
  margin-bottom: 10rpx;
}

.shenwei-value {
  display: block;
  font-size: 28rpx;
  color: #FF0000;
  font-weight: bold;
}

/* 功能按钮 */
.function-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 40rpx;
}

.function-btn {
  flex: 1;
  margin: 0 10rpx;
  background: #FF0000;
  color: white;
  border: none;
  border-radius: 20rpx;
  padding: 30rpx 0;
  box-shadow: 0 4rpx 15rpx rgba(255, 0, 0, 0.3);
}

.function-btn:active {
  background: #CC0000;
  transform: scale(0.98);
}

.btn-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.btn-icon {
  font-size: 36rpx;
  margin-bottom: 8rpx;
}

.btn-text {
  font-size: 26rpx;
  color: white;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-text {
  background: white;
  padding: 30rpx 40rpx;
  border-radius: 15rpx;
  font-size: 28rpx;
  color: #333;
}
</style>
