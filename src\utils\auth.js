import { StorageUtil } from './storage.js'

// 认证工具类
export class AuthUtil {
  // 默认密码
  static DEFAULT_PASSWORD = '11419131'

  // 初始化密码（首次使用时设置默认密码）
  static initPassword() {
    const existingPassword = StorageUtil.getPassword()
    if (!existingPassword) {
      StorageUtil.setPassword(this.DEFAULT_PASSWORD)
    }
  }

  // 验证密码
  static verifyPassword(inputPassword) {
    const storedPassword = StorageUtil.getPassword()
    return inputPassword === storedPassword
  }

  // 修改密码
  static changePassword(newPassword) {
    StorageUtil.setPassword(newPassword)
  }

  // 登录
  static login(password) {
    if (this.verifyPassword(password)) {
      StorageUtil.setLoginStatus(true)
      return true
    }
    return false
  }

  // 登出
  static logout() {
    StorageUtil.setLoginStatus(false)
  }

  // 检查登录状态
  static checkLoginStatus() {
    return StorageUtil.isLoggedIn()
  }

  // 需要登录验证的页面检查
  static requireAuth() {
    if (!this.checkLoginStatus()) {
      uni.reLaunch({
        url: '/pages/login/login'
      })
      return false
    }
    return true
  }
}
