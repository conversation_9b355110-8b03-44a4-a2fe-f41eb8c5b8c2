// 历法工具类 - 基于lunar-javascript库
import { Solar, Lunar } from 'lunar-javascript'
import { StorageUtil } from './storage.js'

export class CalendarUtil {
  // 佛教节日数据
  static BUDDHIST_FESTIVALS = {
    '农历四月初八': '佛诞日(释迦牟尼佛诞)',
    '农历二月十九': '观音诞',
    '农历六月十九': '观音成道日',
    '农历九月十九': '观音出家日',
    '农历七月三十': '地藏诞',
    '农历正月初一': '弥勒佛诞',
    '农历七月十五': '盂兰盆节',
    '农历十二月初八': '腊八节(佛成道日)'
  }
  
  // 道教节日数据
  static TAOIST_FESTIVALS = {
    '农历正月初九': '玉皇大帝诞',
    '农历二月十五': '太上老君诞',
    '农历三月初三': '王母娘娘诞',
    '农历七月十五': '中元节',
    '农历九月初九': '重阳节',
    '农历十月十五': '下元节',
    '农历十二月二十三': '灶王爷诞'
  }

  // 获取指定日期的完整历法信息
  static getCalendarInfo(date = new Date()) {
    try {
      const solar = Solar.fromDate(date)
      const lunar = solar.getLunar()
      
      return {
        solar: {
          year: solar.getYear(),
          month: solar.getMonth(),
          day: solar.getDay(),
          weekday: solar.getWeek(),
          constellation: solar.getXingZuo(),
          isLeapYear: solar.isLeapYear()
        },
        lunar: {
          year: lunar.getYear(),
          month: lunar.getMonth(),
          day: lunar.getDay(),
          yearInChinese: lunar.getYearInChinese(),
          monthInChinese: lunar.getMonthInChinese(),
          dayInChinese: lunar.getDayInChinese(),
          isLeapMonth: lunar.getMonth() < 0 // 负数表示闰月
        },
        ganzhi: {
          year: lunar.getYearInGanZhi(),
          month: lunar.getMonthInGanZhi(),
          day: lunar.getDayInGanZhi(),
          time: lunar.getTimeInGanZhi(date.getHours())
        },
        zodiac: lunar.getYearShengXiao(),
        jieqi: this.getJieQiFromString(solar.toFullString()),
        yi: lunar.getDayYi() || [],
        ji: lunar.getDayJi() || [],
        shenwei: {
          xi: lunar.getDayPositionXi() || '未知',
          cai: lunar.getDayPositionCai() || '未知',
          fu: lunar.getDayPositionFu() || '未知',
          yangGui: lunar.getDayPositionYangGui() || '未知',
          yinGui: lunar.getDayPositionYinGui() || '未知'
        },
        nayin: lunar.getDayNaYin() || '未知',
        xingxiu: lunar.getXiu() || '未知',
        chong: lunar.getDayChong() || '无',
        sha: lunar.getDaySha() || '无',
        pengzu: lunar.getPengZuGan() + ' ' + lunar.getPengZuZhi(),
        festivals: this.getFestivals(lunar, solar)
      }
    } catch (error) {
      console.error('获取历法信息失败:', error)
      return this.getDefaultCalendarInfo(date)
    }
  }

  // 从完整字符串中提取节气信息
  static getJieQiFromString(fullString) {
    // 从完整字符串中提取节气信息
    const jieqiMatch = fullString.match(/\((.*?节|.*?至)\)/)
    return jieqiMatch ? jieqiMatch[1] : ''
  }

  // 从完整字符串中提取节日信息
  static getFestivalFromString(fullString) {
    // 从完整字符串中提取节日信息
    const festivalMatch = fullString.match(/\((.*?节|.*?日)\)/)
    if (festivalMatch && !festivalMatch[1].includes('节气')) {
      return festivalMatch[1]
    }
    return ''
  }

  // 获取节日信息
  static getFestivals(lunar, solar) {
    const festivals = []

    // 农历节日
    const lunarDate = `农历${lunar.getMonthInChinese()}${lunar.getDayInChinese()}`
    if (this.BUDDHIST_FESTIVALS[lunarDate]) {
      festivals.push({
        type: 'buddhist',
        name: this.BUDDHIST_FESTIVALS[lunarDate]
      })
    }
    if (this.TAOIST_FESTIVALS[lunarDate]) {
      festivals.push({
        type: 'taoist',
        name: this.TAOIST_FESTIVALS[lunarDate]
      })
    }

    // 从完整字符串中提取节日
    const solarFestival = this.getFestivalFromString(solar.toFullString())
    if (solarFestival) {
      festivals.push({
        type: 'solar',
        name: solarFestival
      })
    }

    const lunarFestival = this.getFestivalFromString(lunar.toFullString())
    if (lunarFestival && lunarFestival !== solarFestival) {
      festivals.push({
        type: 'lunar',
        name: lunarFestival
      })
    }

    return festivals
  }

  // 生成月份日历数据
  static generateMonthDays(date) {
    try {
      const year = date.getFullYear()
      const month = date.getMonth()
      const firstDay = new Date(year, month, 1)
      const lastDay = new Date(year, month + 1, 0)
      const startDate = new Date(firstDay)
      startDate.setDate(startDate.getDate() - firstDay.getDay())
      
      const days = []
      const today = new Date()
      
      for (let i = 0; i < 42; i++) {
        const currentDate = new Date(startDate)
        currentDate.setDate(startDate.getDate() + i)
        
        const solar = Solar.fromDate(currentDate)
        const lunar = solar.getLunar()
        
        days.push({
          key: `${currentDate.getFullYear()}-${currentDate.getMonth()}-${currentDate.getDate()}`,
          date: new Date(currentDate),
          solar: currentDate.getDate(),
          lunar: lunar.getDayInChinese(),
          isToday: this.isSameDay(currentDate, today),
          isOtherMonth: currentDate.getMonth() !== month,
          festivals: this.getFestivals(lunar, solar),
          jieqi: this.getJieQiFromString(solar.toFullString())
        })
      }
      
      return days
    } catch (error) {
      console.error('生成月份日历失败:', error)
      return []
    }
  }

  // 获取佛历信息
  static getBuddhistInfo(date = new Date()) {
    try {
      const solar = Solar.fromDate(date)
      const lunar = solar.getLunar()
      
      // 佛历计算（以释迦牟尼佛涅槃后计算，一般认为是公元前543年）
      const buddhistYear = solar.getYear() + 543
      
      return {
        year: buddhistYear,
        festivals: this.getBuddhistFestivals(lunar),
        zhaiDays: this.getZhaiDays(lunar),
        practice: this.getDailyPractice(lunar)
      }
    } catch (error) {
      console.error('获取佛历信息失败:', error)
      return {
        year: new Date().getFullYear() + 543,
        festivals: [],
        zhaiDays: null,
        practice: '宜念佛诵经，修心养性'
      }
    }
  }

  // 获取道历信息
  static getTaoistInfo(date = new Date()) {
    try {
      const solar = Solar.fromDate(date)
      const lunar = solar.getLunar()
      
      return {
        festivals: this.getTaoistFestivals(lunar),
        luckyDays: this.getTaoistLuckyDays(lunar),
        yangsheng: this.getYangshengAdvice(solar)
      }
    } catch (error) {
      console.error('获取道历信息失败:', error)
      return {
        festivals: [],
        luckyDays: null,
        yangsheng: '宜静心修炼，顺应自然'
      }
    }
  }

  // 获取斋戒日信息
  static getZhaiDays(lunar) {
    const day = lunar.getDay()
    const zhaiDays = [1, 8, 14, 15, 18, 23, 24, 28, 29, 30]
    return zhaiDays.includes(day) ? `今日是斋戒日（农历${lunar.getDayInChinese()}）` : null
  }

  // 获取每日修行建议
  static getDailyPractice(lunar) {
    const practices = [
      '宜念佛诵经，修心养性',
      '宜静坐冥想，观照内心',
      '宜行善布施，积累功德',
      '宜持戒修行，净化身心',
      '宜礼佛拜忏，消除业障',
      '宜读经闻法，增长智慧'
    ]
    return practices[lunar.getDay() % practices.length]
  }

  // 获取道教养生建议
  static getYangshengAdvice(solar) {
    const hour = new Date().getHours()
    const advices = {
      '子时': '宜静坐调息，养精蓄锐',
      '丑时': '宜安眠休息，肝胆排毒',
      '寅时': '宜深度睡眠，肺经当令',
      '卯时': '宜起床活动，大肠经当令',
      '辰时': '宜进食早餐，胃经当令',
      '巳时': '宜工作学习，脾经当令',
      '午时': '宜小憩片刻，心经当令',
      '未时': '宜午休调养，小肠经当令',
      '申时': '宜适度运动，膀胱经当令',
      '酉时': '宜收敛精神，肾经当令',
      '戌时': '宜放松身心，心包经当令',
      '亥时': '宜准备就寝，三焦经当令'
    }
    
    const timeNames = ['子时', '丑时', '寅时', '卯时', '辰时', '巳时', 
                      '午时', '未时', '申时', '酉时', '戌时', '亥时']
    const timeIndex = Math.floor(hour / 2)
    const timeName = timeNames[timeIndex] || '子时'
    
    return advices[timeName] || '宜静心修炼，顺应自然'
  }

  // 判断是否同一天
  static isSameDay(date1, date2) {
    return date1.getFullYear() === date2.getFullYear() &&
           date1.getMonth() === date2.getMonth() &&
           date1.getDate() === date2.getDate()
  }

  // 获取默认历法信息（错误时使用）
  static getDefaultCalendarInfo(date) {
    return {
      solar: {
        year: date.getFullYear(),
        month: date.getMonth() + 1,
        day: date.getDate(),
        weekday: ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'][date.getDay()],
        constellation: '未知',
        isLeapYear: false
      },
      lunar: {
        year: date.getFullYear(),
        month: date.getMonth() + 1,
        day: date.getDate(),
        yearInChinese: '未知年',
        monthInChinese: '未知月',
        dayInChinese: '未知日',
        isLeapMonth: false
      },
      ganzhi: {
        year: '未知',
        month: '未知',
        day: '未知',
        time: '未知'
      },
      zodiac: '未知',
      jieqi: '',
      yi: [],
      ji: [],
      shenwei: {
        xi: '未知',
        cai: '未知',
        fu: '未知'
      },
      festivals: []
    }
  }

  // 获取佛教节日
  static getBuddhistFestivals(lunar) {
    const lunarDate = `农历${lunar.getMonthInChinese()}${lunar.getDayInChinese()}`
    const festival = this.BUDDHIST_FESTIVALS[lunarDate]
    return festival ? [{ name: festival, type: 'buddhist' }] : []
  }

  // 获取道教节日
  static getTaoistFestivals(lunar) {
    const lunarDate = `农历${lunar.getMonthInChinese()}${lunar.getDayInChinese()}`
    const festival = this.TAOIST_FESTIVALS[lunarDate]
    return festival ? [{ name: festival, type: 'taoist' }] : []
  }

  // 获取道教吉日
  static getTaoistLuckyDays(lunar) {
    const day = lunar.getDay()
    const luckyDays = [1, 3, 6, 8, 9, 15, 18, 23, 28]
    return luckyDays.includes(day) ? `今日宜修炼（农历${lunar.getDayInChinese()}）` : null
  }
}
