<template>
  <view class="records-container" @click="resetSwipe">
    <view class="header">
      <text class="title">记录</text>
      <text class="subtitle">查看三种模式的历史记录</text>
    </view>

    <!-- 搜索框 -->
    <view class="search-section">
      <view class="search-box">
        <input 
          class="search-input"
          v-model="searchKeyword"
          placeholder="搜索关键字"
          @input="handleSearch"
        />
        <view class="search-icon">🔍</view>
      </view>
    </view>

    <!-- 模式分类按钮 -->
    <view class="mode-tabs">
      <view 
        class="tab-item"
        :class="{ active: activeTab === 'all' }"
        @click="switchTab('all')"
      >
        全部记录
      </view>
      <view 
        class="tab-item"
        :class="{ active: activeTab === 'mode1' }"
        @click="switchTab('mode1')"
      >
        数字能量
      </view>
      <view 
        class="tab-item"
        :class="{ active: activeTab === 'mode2' }"
        @click="switchTab('mode2')"
      >
        梅花易数
      </view>
      <view
        class="tab-item"
        :class="{ active: activeTab === 'mode3' }"
        @click="switchTab('mode3')"
      >
        自由组数
      </view>
      <view
        class="tab-item"
        :class="{ active: activeTab === 'mode4' }"
        @click="switchTab('mode4')"
      >
        随机数字
      </view>
    </view>

    <!-- 记录列表 -->
    <view class="records-list">
      <view v-if="filteredRecords.length === 0" class="empty-state">
        <text class="empty-text">{{ searchKeyword ? '没有找到相关记录' : '暂无记录' }}</text>
      </view>

      <view
        v-for="record in filteredRecords"
        :key="record.id"
        class="record-wrapper"
      >
        <view
          class="record-item"
          :class="{ 'swiped': swipedRecordId === record.id }"
          @touchstart="handleTouchStart($event, record)"
          @touchmove="handleTouchMove($event, record)"
          @touchend="handleTouchEnd($event, record)"
          @longpress="showDeleteDialog(record)"
        >
          <view class="record-content-wrapper">
            <view class="record-header">
              <view class="record-time">{{ record.timestamp }}</view>
              <view class="record-mode">{{ getModeText(record.mode) }}</view>
            </view>
            <view class="record-content">
              <text class="record-text">{{ record.text }}</text>
              <text class="record-result" @click="copyRecordResult(record.result)">{{ record.result }}</text>
            </view>
          </view>
        </view>

        <!-- 删除按钮 -->
        <view
          class="delete-action"
          :class="{ 'show': swipedRecordId === record.id }"
          @click.stop="deleteRecord(record)"
        >
          <text class="delete-text">删除</text>
        </view>
      </view>
    </view>

    <!-- 删除确认弹窗 -->
    <!-- <view v-if="showDeleteDialog" class="delete-modal" @click="cancelDelete">
      <view class="delete-dialog" @click.stop>
        <view class="dialog-title">确认删除</view>
        <view class="dialog-content">确定要删除这条记录吗？</view>
        <view class="dialog-buttons">
          <button class="cancel-btn" @click="cancelDelete">取消</button>
          <button class="confirm-btn" @click="confirmDelete">确定</button>
        </view>
      </view>
    </view> -->
  </view>
</template>

<script>
import { AuthUtil } from '@/utils/auth.js'
import { StorageUtil } from '@/utils/storage.js'

export default {
  data() {
    return {
      searchKeyword: '',
      activeTab: 'all',
      allRecords: [],
      filteredRecords: [],
      recordToDelete: null,
      showDeleteDialog: false,
      // 左滑删除相关
      swipedRecordId: null,
      startX: 0,
      startY: 0,
      moveX: 0,
      isMoving: false
    }
  },

  onLoad() {
    AuthUtil.requireAuth()
    this.loadRecords()
  },

  onShow() {
    this.loadRecords()
  },

  methods: {
    loadRecords() {
      try {
        this.allRecords = StorageUtil.getRecords()
        this.filterRecords()
      } catch (e) {
        console.error('加载记录失败:', e)
        this.allRecords = []
        this.filteredRecords = []
        // 不显示错误提示，静默处理
      }
    },

    switchTab(tab) {
      this.activeTab = tab
      this.filterRecords()
    },

    handleSearch() {
      this.filterRecords()
    },

    filterRecords() {
      let records = this.allRecords

      // 按模式筛选
      if (this.activeTab !== 'all') {
        records = records.filter(record => record.mode === this.activeTab)
      }

      // 按关键字搜索
      if (this.searchKeyword) {
        records = records.filter(record => 
          record.text.toLowerCase().includes(this.searchKeyword.toLowerCase())
        )
      }

      this.filteredRecords = records
    },

    getModeText(mode) {
      const modeMap = {
        'mode1': '数字能量',
        'mode2': '梅花易数',
        'mode3': '自由组数',
        'mode4': '随机数字'
      }
      return modeMap[mode] || mode
    },

    showDeleteDialog(record) {
      this.recordToDelete = record
      this.showDeleteDialog = true
    },

    confirmDelete() {
      if (this.recordToDelete) {
        const success = StorageUtil.deleteRecord(this.recordToDelete.id)
        if (success) {
          this.loadRecords()
          uni.showToast({
            title: '删除成功',
            icon: 'success'
          })
        } else {
          uni.showToast({
            title: '删除失败，请重试',
            icon: 'none'
          })
        }
      }
      this.cancelDelete()
    },

    cancelDelete() {
      this.recordToDelete = null
      this.showDeleteDialog = false
    },

    // 左滑删除相关方法
    handleTouchStart(e, record) {
      this.startX = e.touches[0].clientX
      this.startY = e.touches[0].clientY
      this.isMoving = false
    },

    handleTouchMove(e, record) {
      if (!this.isMoving) {
        const moveX = e.touches[0].clientX - this.startX
        const moveY = e.touches[0].clientY - this.startY

        // 判断是否为水平滑动
        if (Math.abs(moveX) > Math.abs(moveY) && Math.abs(moveX) > 10) {
          this.isMoving = true
          this.moveX = moveX
        }
      }

      if (this.isMoving) {
        e.preventDefault()
        this.moveX = e.touches[0].clientX - this.startX
      }
    },

    handleTouchEnd(e, record) {
      if (this.isMoving) {
        // 左滑超过80px显示删除按钮
        if (this.moveX < -80) {
          this.swipedRecordId = record.id
        } else {
          this.swipedRecordId = null
        }
      }

      this.isMoving = false
      this.moveX = 0
    },

    // 直接删除记录（左滑删除）
    deleteRecord(record) {
      const success = StorageUtil.deleteRecord(record.id)
      if (success) {
        this.loadRecords()
        this.swipedRecordId = null
        uni.showToast({
          title: '删除成功',
          icon: 'success'
        })
      } else {
        uni.showToast({
          title: '删除失败，请重试',
          icon: 'none'
        })
      }
    },

    // 重置滑动状态
    resetSwipe() {
      this.swipedRecordId = null
    },

    // 复制记录结果到剪贴板
    copyRecordResult(result) {
      if (result) {
        uni.setClipboardData({
          data: result.toString(),
          fail: () => {
            uni.showToast({
              title: '复制失败',
              icon: 'none',
              duration: 1500
            })
          }
        })
      }
    }
  }
}
</script>

<style scoped>
.records-container {
  min-height: 100vh;
  background: #F5F5F5;
  padding: 40rpx;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: #666;
}

.search-section {
  margin-bottom: 40rpx;
}

.search-box {
  position: relative;
  background: white;
  border-radius: 50rpx;
  padding: 0 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.search-input {
  width: 100%;
  height: 80rpx;
  font-size: 30rpx;
  padding-right: 60rpx;
}

.search-icon {
  position: absolute;
  right: 40rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 32rpx;
  color: #999;
}

.mode-tabs {
  display: flex;
  background: white;
  border-radius: 50rpx;
  padding: 10rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.tab-item {
  flex: 1;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  color: #666;
  border-radius: 40rpx;
  transition: all 0.3s ease;
}

.tab-item.active {
  background: #FF0000;
  color: white;
}

.records-list {
  margin-bottom: 40rpx;
}

.empty-state {
  text-align: center;
  padding: 100rpx 0;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

.record-wrapper {
  position: relative;
  margin-bottom: 20rpx;
  overflow: hidden;
  border-radius: 20rpx;
}

.record-item {
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 2;
  transition: transform 0.3s ease;
}

.record-item.swiped {
  transform: translateX(-160rpx);
}

.record-content-wrapper {
  padding: 30rpx;
}

.delete-action {
  position: absolute;
  right: -160rpx;
  top: 0;
  bottom: 0;
  width: 160rpx;
  background: #FF4444;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0 20rpx 20rpx 0;
  transition: right 0.3s ease;
}

.delete-action.show {
  right: 0;
}

.delete-text {
  color: white;
  font-size: 28rpx;
  font-weight: bold;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.record-time {
  font-size: 24rpx;
  color: #999;
}

.record-mode {
  font-size: 24rpx;
  color: #FF0000;
  background: rgba(255, 0, 0, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.record-content {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.record-text {
  font-size: 30rpx;
  color: #333;
  word-break: break-all;
}

.record-result {
  font-size: 36rpx;
  font-weight: bold;
  color: #FF0000;
  cursor: pointer;
  user-select: none;
  transition: opacity 0.2s ease;
}

.record-result:active {
  opacity: 0.7;
}

.delete-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.delete-dialog {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin: 40rpx;
  max-width: 500rpx;
  width: 100%;
}

.dialog-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 20rpx;
}

.dialog-content {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  margin-bottom: 40rpx;
}

.dialog-buttons {
  display: flex;
  gap: 20rpx;
}

.cancel-btn, .confirm-btn {
  flex: 1;
  height: 80rpx;
  border: none;
  border-radius: 40rpx;
  font-size: 30rpx;
}

.cancel-btn {
  background: #f5f5f5;
  color: #666;
}

.confirm-btn {
  background: #FF0000;
  color: white;
}
</style>
