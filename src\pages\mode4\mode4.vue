<template>
  <view class="mode-container">
    <view class="header">
      <text class="mode-title">问</text>
      
    </view>

    <view class="content">
      <!-- 结果显示区域 -->
      <view class="result-section" v-if="showResult">
        <view class="result-number" @click="copyResult">{{ currentResult }}</view>
        <view class="result-time">{{ currentTime }}</view>
      </view>

      <!-- 确定按钮 -->
      <view class="button-section">
        <button class="confirm-btn" @click="generateNumber">
          确定
        </button>
      </view>

      <!-- 历史记录按钮 -->
      <view class="history-section">
        <button class="history-btn" @click="viewHistory">
          查看随机数字记录
        </button>
      </view>
    </view>
  </view>
</template>

<script>
import { AuthUtil } from '@/utils/auth.js'
import { RandomUtil } from '@/utils/random.js'
import { StorageUtil } from '@/utils/storage.js'

export default {
  data() {
    return {
      showResult: false,
      currentResult: '',
      currentTime: ''
    }
  },

  onLoad() {
    AuthUtil.requireAuth()
  },

  methods: {
    generateNumber() {


      // 延时1.5秒后生成随机数
      setTimeout(() => {
        // 生成随机数
        const result = RandomUtil.generateMode4()
        const timestamp = RandomUtil.getCurrentTimestamp()
        
        // 显示结果
        this.currentResult = result
        this.currentTime = timestamp
        this.showResult = true

        // 保存记录
        const record = RandomUtil.createRecord('mode4', '随机数字', result)
        StorageUtil.saveRecord(record)

        // 隐藏加载提示并显示成功提示
        uni.hideLoading()

      }, 1500)
    },

    viewHistory() {
      uni.navigateTo({
        url: '/pages/mode-records/mode-records?mode=mode4&title=随机数字记录'
      })
    },

    // 复制结果到剪贴板
    copyResult() {
      if (this.currentResult) {
        uni.setClipboardData({
          data: this.currentResult.toString(),
          fail: () => {
            uni.showToast({
              title: '复制失败',
              icon: 'none',
              duration: 1500
            })
          }
        })
      }
    }
  }
}
</script>

<style scoped>
.mode-container {
  min-height: 100vh;
  background: #F5F5F5;
  padding: 40rpx;
}

.header {
  text-align: center;
  margin-bottom: 60rpx;
}

.mode-title {
  display: block;
  font-size: 60rpx;
  font-weight: bold;
  color: #ff0000;
  margin-bottom: 20rpx;
  margin-top: 20rpx;
}

.mode-desc {
  display: block;
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

.content {
  max-width: 600rpx;
  margin: 0 auto;
}

.result-section {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx 40rpx 30rpx 40rpx;
  text-align: center;
  margin-bottom: 60rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.result-number {
  font-size: 240rpx;
  font-weight: bold;
  color: #FF0000;
  margin-bottom: 30rpx;
  margin-top: 40rpx;
  cursor: pointer;
  user-select: none;
  transition: opacity 0.2s ease;
}

.result-number:active {
  opacity: 0.7;
}

.result-time {
  font-size: 24rpx;
  color: #999;
}

.button-section {
  position: fixed;
  bottom: 200rpx;
  left: 50%;
  transform: translateX(-50%);
  width: calc(100% - 80rpx);
  max-width: 600rpx;
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  z-index: 999;
}

.confirm-btn {
  width: 100%;
  height: 100rpx;
  background: #FF0000;
  color: white;
  border: none;
  border-radius: 50rpx;
  font-size: 36rpx;
  font-weight: bold;
}

.history-section {
  position: fixed;
  bottom: 80rpx;
  left: 50%;
  transform: translateX(-50%);
  width: calc(100% - 80rpx);
  max-width: 600rpx;
  text-align: center;
  z-index: 999;
}

.history-btn {
  width: 100%;
  height: 80rpx;
  background: transparent;
  color: #FF0000;
  border: 2rpx solid #FF0000;
  border-radius: 40rpx;
  font-size: 30rpx;
}
</style>
