# 随机数生成器

一个基于 Vue3 + uni-app 的随机数生成器应用，支持三种不同的随机数生成模式。

## 功能特性

### 三种生成模式

1. **模式一**：生成 1-99 的随机数
2. **模式二**：生成时间格式随机数 (00:00-23:59)
3. **模式三**：生成自定义位数随机数 (1-13位)

### 核心功能

- 🔐 **密码保护**：首次使用需要输入密码（默认：123456）
- 📝 **文字输入**：每次生成前可输入最多50个字的描述文字
- 📊 **历史记录**：自动保存所有生成记录，包含时间戳
- 🔍 **搜索功能**：支持按关键字搜索历史记录
- 📱 **分类查看**：可按模式分别查看历史记录
- 🗑️ **删除功能**：长按记录可删除不需要的记录

## 使用说明

### 首次使用

1. 打开应用后会显示登录页面
2. 输入默认密码：`123456`
3. 登录成功后进入主页面

### 生成随机数

1. 在主页面选择想要的模式（模式一、模式二、模式三）
2. 输入描述文字（最多50个字）
3. 对于模式三，还需要选择位数（1-13位）
4. 点击"确定"按钮生成随机数
5. 结果会显示在页面上，并自动保存到历史记录

### 查看历史记录

1. 在主页面点击"记录"按钮查看所有记录
2. 可以按模式筛选记录（全部记录、模式一、模式二、模式三）
3. 使用搜索框按关键字搜索记录
4. 长按任意记录可以删除

### 单个模式记录

1. 在各个模式页面点击"查看模式X记录"按钮
2. 查看该模式下的所有历史记录
3. 支持搜索和删除功能

## 技术栈

- **前端框架**：Vue 3
- **跨平台框架**：uni-app
- **构建工具**：Vite
- **样式**：原生 CSS
- **存储**：uni-app 本地存储

## 开发和运行

### 安装依赖

```bash
npm install
```

### 开发模式

```bash
# H5 开发
npm run dev:h5

# 微信小程序开发
npm run dev:mp-weixin

# 其他平台请查看 package.json 中的脚本
```

### 构建发布

```bash
# H5 构建
npm run build:h5

# 微信小程序构建
npm run build:mp-weixin
```

## 项目结构

```
src/
├── pages/                 # 页面文件
│   ├── login/            # 登录页面
│   ├── home/             # 主页面
│   ├── mode1/            # 模式一页面
│   ├── mode2/            # 模式二页面
│   ├── mode3/            # 模式三页面
│   ├── records/          # 记录页面
│   └── mode-records/     # 单个模式记录页面
├── utils/                # 工具类
│   ├── auth.js          # 认证工具
│   ├── storage.js       # 存储工具
│   └── random.js        # 随机数生成工具
├── pages.json           # 页面配置
└── App.vue             # 应用入口
```

## 注意事项

1. 默认密码为 `123456`，建议在实际使用中修改
2. 所有数据存储在本地，卸载应用会丢失数据
3. 支持多平台运行（H5、小程序、APP等）
4. 长按记录可以删除，请谨慎操作

## 更新日志

### v1.0.0
- 实现三种随机数生成模式
- 添加密码保护功能
- 实现历史记录保存和查看
- 添加搜索和删除功能
- 完成基础UI设计
