// 随机数生成工具类
export class RandomUtil {
  // 生成指定范围的随机整数
  static randomInt(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min
  }

  // 模式一：生成1-99的随机数
  static generateMode1() {
    return this.randomInt(1, 99)
  }

  // 模式二：生成时间格式的随机数 (00:00 - 23:59)
  static generateMode2() {
    const hours = this.randomInt(0, 23)
    const minutes = this.randomInt(0, 59)
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`
  }

  // 模式三：生成指定位数的随机数
  static generateMode3(digits) {
    if (digits < 1 || digits > 13) {
      throw new Error('位数必须在1-13之间')
    }
    
    let result = ''
    
    // 第一位不能为0（除非是1位数且允许为0）
    if (digits === 1) {
      result = this.randomInt(0, 9).toString()
    } else {
      result = this.randomInt(1, 9).toString()
      
      // 生成剩余位数
      for (let i = 1; i < digits; i++) {
        result += this.randomInt(0, 9).toString()
      }
    }
    
    return result
  }

  // 模式四：生成1-99的随机数（与模式一相同，但不需要输入文字）
  static generateMode4() {
    return this.randomInt(1, 99)
  }

  // 生成唯一ID
  static generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }

  // 获取当前时间戳字符串
  static getCurrentTimestamp() {
    const now = new Date()
    const year = now.getFullYear()
    const month = (now.getMonth() + 1).toString().padStart(2, '0')
    const day = now.getDate().toString().padStart(2, '0')
    const hours = now.getHours().toString().padStart(2, '0')
    const minutes = now.getMinutes().toString().padStart(2, '0')
    
    return `${month}月${day}日 ${hours}:${minutes}`
  }

  // 创建记录对象
  static createRecord(mode, text, result) {
    return {
      id: this.generateId(),
      mode: mode,
      text: text,
      result: result,
      timestamp: this.getCurrentTimestamp(),
      createTime: Date.now()
    }
  }
}
