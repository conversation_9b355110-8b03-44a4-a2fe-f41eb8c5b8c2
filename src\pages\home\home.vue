<template>
  <view class="home-container">
    <view class="header">
      <text class="title">易道数</text>
    </view>

    <view class="mode-list">
      <view class="mode-item" @click="navigateToMode(1)">
      <view class="mode-icon">
          <image src="/src/static/2.png" class="mode-icon-image"></image>
        </view>
        <view class="mode-info">
          <text class="mode-title">数字能量</text>
        </view>
        <view class="arrow">></view>
      </view>

      <view class="mode-item" @click="navigateToMode(2)">
        <view class="mode-icon">
          <image src="/src/static/2.png" class="mode-icon-image"></image>
        </view>
        <view class="mode-info">
          <text class="mode-title">梅花易数</text>
        </view>
        <view class="arrow">></view>
      </view>

      <view class="mode-item" @click="navigateToMode(3)">
      <view class="mode-icon">
          <image src="/src/static/2.png" class="mode-icon-image"></image>
        </view>
        <view class="mode-info">
          <text class="mode-title">自由组数</text>
        </view>
        <view class="arrow">></view>
      </view>

      <view class="mode-item" @click="navigateToMode(4)">
    <view class="mode-icon">
      <image src="/src/static/2.png" class="mode-icon-image"></image>
    </view>
        <view class="mode-info">
          <text class="mode-title">随机数字</text>
        </view>
        <view class="arrow">></view>
      </view>

      <!-- 中华万年历入口 -->
      <view class="mode-item calendar-item" @click="navigateToCalendar">
        <view class="mode-icon">
          <text class="calendar-icon">📅</text>
        </view>
        <view class="mode-info">
          <text class="mode-title">中华万年历</text>
          <text class="mode-subtitle">传统历法 · 文化传承</text>
        </view>
        <view class="arrow">></view>
      </view>
    </view>

    <view class="bottom-section">
      <button class="records-btn" @click="navigateToRecords">
        📋 记&nbsp;&nbsp;&nbsp;&nbsp;录
      </button>
    </view>
    
    <view class="logo-container">
      <image src="/src/static/logo2.png" class="logo-image"></image>
      <text class="version-text">易道数1.0</text>
    </view>
  </view>
</template>

<script>
import { AuthUtil } from '@/utils/auth.js'

export default {
  onLoad() {
    // 检查登录状态
    AuthUtil.requireAuth()
  },

  onShow() {
    // 每次显示页面时检查登录状态
    AuthUtil.requireAuth()
  },

  methods: {
    navigateToMode(mode) {
      const urls = {
        1: '/pages/mode1/mode1',
        2: '/pages/mode2/mode2',
        3: '/pages/mode3/mode3',
        4: '/pages/mode4/mode4'
      }

      uni.navigateTo({
        url: urls[mode]
      })
    },

    navigateToRecords() {
      uni.navigateTo({
        url: '/pages/records/records'
      })
    },

    // 跳转到中华万年历
    navigateToCalendar() {
      uni.navigateTo({
        url: '/pages/calendar/calendar'
      })
    }
  }
}
</script>

<style scoped>
.home-container {
  min-height: 100vh;
  background: #F5F5F5;
  padding: 40rpx;
}

.header {
  text-align: center;
  margin-bottom: 80rpx;
}

.title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: #666;
}

.mode-list {
  margin-bottom: 80rpx;
}

.mode-item {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.mode-item:active {
  transform: scale(0.98);
  background: #f8f8f8;
}

.mode-icon {
  font-size: 60rpx;
  margin-right: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mode-icon-image {
  width: 60rpx;
  height: 60rpx;
}

.mode-info {
  flex: 1;
}

.mode-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.mode-desc {
  display: block;
  font-size: 26rpx;
  color: #666;
}

.mode-subtitle {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-top: 5rpx;
}

.arrow {
  font-size: 32rpx;
  color: #999;
}

/* 万年历按钮特殊样式 */
.calendar-item {
  background: linear-gradient(135deg, #FF0000 0%, #CC0000 100%);
  border: 2rpx solid #FF0000;
}

.calendar-item .mode-title {
  color: white;
  font-weight: bold;
}

.calendar-item .mode-subtitle {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 5rpx;
}

.calendar-item .arrow {
  color: white;
  font-weight: bold;
}

.calendar-icon {
  font-size: 48rpx;
  color: white;
}

.bottom-section {
  text-align: center;
  margin-bottom: 40rpx;
}

.records-btn {
  width: 100%;
  height: 100rpx;
  background: #FF0000;
  color: white;
  border: none;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.records-hint {
  display: block;
  font-size: 24rpx;
  color: #999;
}

.logo-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-top: 20rpx;
}

.logo-image {
  /* width: 25%; */
  width: 80rpx;
 height: 80rpx;
  /* height: auto; */
  margin-bottom: 10rpx;
}

.version-text {
  font-size: 24rpx;
  color: #999;
  text-align: center;
}
</style> 