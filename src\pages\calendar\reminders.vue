<template>
  <view class="reminders-container">
    <!-- 头部标题 -->
    <view class="header">
      <text class="title">定时提醒</text>
      <text class="subtitle">传统节日 · 个人提醒</text>
    </view>

    <!-- 添加提醒按钮 -->
    <view class="add-section">
      <button class="add-btn" @click="showAddReminder">
        <text class="add-icon">+</text>
        <text class="add-text">添加新提醒</text>
      </button>
    </view>

    <!-- 提醒列表 -->
    <view class="reminders-list" v-if="reminders.length > 0">
      <view class="list-header">
        <text class="list-title">我的提醒 ({{ enabledCount }}/{{ reminders.length }})</text>
        <button class="cleanup-btn" @click="cleanupExpired">清理过期</button>
      </view>
      
      <view 
        v-for="reminder in reminders" 
        :key="reminder.id"
        class="reminder-item"
        :class="{ 'disabled': !reminder.enabled, 'expired': reminder.status === 'expired' }"
        @longpress="showReminderOptions(reminder)"
      >
        <view class="reminder-main" @click="toggleReminder(reminder)">
          <view class="reminder-info">
            <text class="reminder-title">{{ reminder.title }}</text>
            <text class="reminder-content" v-if="reminder.content">{{ reminder.content }}</text>
            <view class="reminder-details">
              <text class="reminder-time">{{ formatReminderTime(reminder) }}</text>
              <text class="reminder-type">{{ getReminderTypeText(reminder.type) }}</text>
            </view>
          </view>
          <view class="reminder-status">
            <switch 
              :checked="reminder.enabled" 
              @change="toggleReminderStatus(reminder)"
              color="#FF0000"
            />
          </view>
        </view>
        
        <view class="reminder-actions">
          <button class="action-btn edit-btn" @click="editReminder(reminder)">编辑</button>
          <button class="action-btn delete-btn" @click="deleteReminder(reminder)">删除</button>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" v-else>
      <text class="empty-icon">⏰</text>
      <text class="empty-title">暂无提醒</text>
      <text class="empty-desc">添加您的第一个提醒，不错过重要时刻</text>
    </view>

    <!-- 快速添加模板 -->
    <view class="quick-templates">
      <view class="templates-title">
        <text>快速添加</text>
      </view>
      <view class="templates-grid">
        <view 
          v-for="template in quickTemplates" 
          :key="template.id"
          class="template-item"
          @click="addFromTemplate(template)"
        >
          <text class="template-icon">{{ template.icon }}</text>
          <text class="template-name">{{ template.name }}</text>
        </view>
      </view>
    </view>

    <!-- 添加/编辑提醒弹窗 -->
    <view v-if="showPopup" class="popup-overlay" @click="closeReminderForm">
      <view class="popup-content" @click.stop>
      <view class="reminder-form">
        <view class="form-header">
          <text class="form-title">{{ isEditing ? '编辑提醒' : '添加提醒' }}</text>
          <button class="form-close" @click="closeReminderForm">×</button>
        </view>
        
        <view class="form-content">
          <view class="form-item">
            <text class="form-label">提醒标题</text>
            <input 
              v-model="formData.title" 
              class="form-input" 
              placeholder="请输入提醒标题"
              maxlength="20"
            />
          </view>
          
          <view class="form-item">
            <text class="form-label">提醒内容</text>
            <textarea 
              v-model="formData.content" 
              class="form-textarea" 
              placeholder="请输入提醒内容（可选）"
              maxlength="100"
            />
          </view>
          
          <view class="form-item">
            <text class="form-label">提醒类型</text>
            <picker 
              :value="reminderTypeIndex" 
              :range="reminderTypeOptions"
              range-key="name"
              @change="onReminderTypeChange"
            >
              <view class="form-picker">
                <text>{{ reminderTypeOptions[reminderTypeIndex].name }}</text>
                <text class="picker-arrow">></text>
              </view>
            </picker>
          </view>
          
          <view class="form-item">
            <text class="form-label">提醒日期</text>
            <picker 
              mode="date" 
              :value="formData.date" 
              @change="onDateChange"
            >
              <view class="form-picker">
                <text>{{ formData.date || '请选择日期' }}</text>
                <text class="picker-arrow">></text>
              </view>
            </picker>
          </view>
          
          <view class="form-item">
            <text class="form-label">提醒时间</text>
            <picker 
              mode="time" 
              :value="formData.time" 
              @change="onTimeChange"
            >
              <view class="form-picker">
                <text>{{ formData.time || '请选择时间' }}</text>
                <text class="picker-arrow">></text>
              </view>
            </picker>
          </view>
          
          <!-- 农历重复选项 -->
          <view class="form-item" v-if="formData.type === 'lunar_monthly'">
            <text class="form-label">农历日期</text>
            <picker 
              :value="lunarDayIndex" 
              :range="lunarDayOptions"
              @change="onLunarDayChange"
            >
              <view class="form-picker">
                <text>{{ lunarDayOptions[lunarDayIndex] }}</text>
                <text class="picker-arrow">></text>
              </view>
            </picker>
          </view>
        </view>
        
        <view class="form-actions">
          <button class="form-btn cancel-btn" @click="closeReminderForm">取消</button>
          <button class="form-btn confirm-btn" @click="saveReminder">{{ isEditing ? '更新' : '添加' }}</button>
        </view>
      </view>
    </view>

    <!-- 返回按钮 -->
    <view class="back-section">
      <button class="back-btn" @click="goBack">
        返回万年历
      </button>
    </view>

    <!-- 加载状态 -->
    <view v-if="loading" class="loading-overlay">
      <text class="loading-text">正在处理...</text>
    </view>
  </view>
</template>

<script>
import { ReminderUtil } from '@/utils/reminder.js'
import { StorageUtil } from '@/utils/storage.js'
import { AuthUtil } from '@/utils/auth.js'
import { CalendarUtil } from '@/utils/calendar.js'

export default {
  data() {
    return {
      reminders: [],
      loading: false,
      isEditing: false,
      currentReminder: null,
      showPopup: false,
      formData: {
        title: '',
        content: '',
        type: 'once',
        date: '',
        time: '',
        lunarDay: null
      },
      reminderTypeOptions: [
        { value: 'once', name: '单次提醒' },
        { value: 'lunar_monthly', name: '农历月重复' },
        { value: 'solar_monthly', name: '公历月重复' },
        { value: 'lunar_yearly', name: '农历年重复' },
        { value: 'solar_yearly', name: '公历年重复' },
        { value: 'weekly', name: '每周重复' },
        { value: 'daily', name: '每日重复' }
      ],
      reminderTypeIndex: 0,
      lunarDayOptions: [],
      lunarDayIndex: 0,
      quickTemplates: [
        { id: 1, name: '初一祈福', icon: '🙏', type: 'lunar_monthly', lunarDay: 1, time: '09:00' },
        { id: 2, name: '十五上香', icon: '🕯️', type: 'lunar_monthly', lunarDay: 15, time: '08:00' },
        { id: 3, name: '每日修行', icon: '📿', type: 'daily', time: '06:00' },
        { id: 4, name: '周末静心', icon: '🧘', type: 'weekly', time: '07:00' }
      ]
    }
  },
  
  computed: {
    enabledCount() {
      return this.reminders.filter(r => r.enabled).length
    }
  },
  
  onLoad() {
    AuthUtil.requireAuth()
    this.initLunarDayOptions()
    this.loadReminders()
  },
  
  onShow() {
    this.loadReminders()
  },
  
  methods: {
    // 初始化农历日期选项
    initLunarDayOptions() {
      this.lunarDayOptions = []
      for (let i = 1; i <= 30; i++) {
        this.lunarDayOptions.push(`农历${i}日`)
      }
    },
    
    // 加载提醒列表
    loadReminders() {
      try {
        this.loading = true
        this.reminders = ReminderUtil.getAllReminders()
        
        // 清理过期提醒
        ReminderUtil.cleanupExpiredReminders()
        
        console.log('加载提醒列表:', this.reminders)
      } catch (error) {
        console.error('加载提醒失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },
    
    // 显示添加提醒
    showAddReminder() {
      this.isEditing = false
      this.currentReminder = null
      this.resetFormData()
      this.showPopup = true
    },
    
    // 编辑提醒
    editReminder(reminder) {
      this.isEditing = true
      this.currentReminder = reminder
      this.formData = {
        title: reminder.title,
        content: reminder.content,
        type: reminder.type,
        date: reminder.date,
        time: reminder.time,
        lunarDay: reminder.lunarDay
      }
      
      // 设置选择器索引
      this.reminderTypeIndex = this.reminderTypeOptions.findIndex(opt => opt.value === reminder.type)
      if (reminder.lunarDay) {
        this.lunarDayIndex = reminder.lunarDay - 1
      }
      
      this.showPopup = true
    },

    // 重置表单数据
    resetFormData() {
      this.formData = {
        title: '',
        content: '',
        type: 'once',
        date: '',
        time: '',
        lunarDay: null
      }
      this.reminderTypeIndex = 0
      this.lunarDayIndex = 0
    },
    
    // 关闭提醒表单
    closeReminderForm() {
      this.showPopup = false
    },
    
    // 保存提醒
    saveReminder() {
      try {
        // 验证表单
        if (!this.formData.title.trim()) {
          uni.showToast({
            title: '请输入提醒标题',
            icon: 'none'
          })
          return
        }
        
        if (!this.formData.date || !this.formData.time) {
          uni.showToast({
            title: '请选择日期和时间',
            icon: 'none'
          })
          return
        }
        
        this.loading = true
        
        const reminderData = {
          title: this.formData.title.trim(),
          content: this.formData.content.trim(),
          type: this.formData.type,
          date: this.formData.date,
          time: this.formData.time,
          lunarDay: this.formData.lunarDay
        }
        
        if (this.isEditing) {
          // 更新提醒
          const updatedReminder = { ...this.currentReminder, ...reminderData }
          const success = ReminderUtil.updateReminder(updatedReminder)
          if (success) {
            uni.showToast({
              title: '更新成功',
              icon: 'success'
            })
          } else {
            throw new Error('更新失败')
          }
        } else {
          // 创建新提醒
          const reminder = ReminderUtil.createReminder(reminderData)
          if (reminder) {
            uni.showToast({
              title: '添加成功',
              icon: 'success'
            })
          } else {
            throw new Error('添加失败')
          }
        }
        
        this.closeReminderForm()
        this.loadReminders()
        
      } catch (error) {
        console.error('保存提醒失败:', error)
        uni.showToast({
          title: error.message || '操作失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },
    
    // 删除提醒
    deleteReminder(reminder) {
      uni.showModal({
        title: '确认删除',
        content: `确定要删除提醒"${reminder.title}"吗？`,
        success: (res) => {
          if (res.confirm) {
            const success = ReminderUtil.cancelReminder(reminder.id)
            if (success) {
              uni.showToast({
                title: '删除成功',
                icon: 'success'
              })
              this.loadReminders()
            } else {
              uni.showToast({
                title: '删除失败',
                icon: 'none'
              })
            }
          }
        }
      })
    },
    
    // 切换提醒状态
    toggleReminderStatus(reminder) {
      reminder.enabled = !reminder.enabled
      const success = ReminderUtil.updateReminder(reminder)
      if (success) {
        this.loadReminders()
      }
    },
    
    // 显示提醒选项
    showReminderOptions(reminder) {
      uni.showActionSheet({
        itemList: ['编辑', '删除', '取消'],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              this.editReminder(reminder)
              break
            case 1:
              this.deleteReminder(reminder)
              break
          }
        }
      })
    },
    
    // 从模板添加
    addFromTemplate(template) {
      this.isEditing = false
      this.currentReminder = null
      this.formData = {
        title: template.name,
        content: '',
        type: template.type,
        date: this.getTodayString(),
        time: template.time,
        lunarDay: template.lunarDay || null
      }
      
      this.reminderTypeIndex = this.reminderTypeOptions.findIndex(opt => opt.value === template.type)
      if (template.lunarDay) {
        this.lunarDayIndex = template.lunarDay - 1
      }
      
      this.showPopup = true
    },
    
    // 清理过期提醒
    cleanupExpired() {
      const count = ReminderUtil.cleanupExpiredReminders()
      uni.showToast({
        title: `清理了${count}个过期提醒`,
        icon: 'success'
      })
      this.loadReminders()
    },
    
    // 格式化提醒时间
    formatReminderTime(reminder) {
      const typeText = this.getReminderTypeText(reminder.type)
      return `${reminder.date} ${reminder.time} (${typeText})`
    },
    
    // 获取提醒类型文本
    getReminderTypeText(type) {
      const option = this.reminderTypeOptions.find(opt => opt.value === type)
      return option ? option.name : '未知类型'
    },
    
    // 获取今天日期字符串
    getTodayString() {
      const today = new Date()
      const year = today.getFullYear()
      const month = String(today.getMonth() + 1).padStart(2, '0')
      const day = String(today.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    },
    
    // 事件处理
    onReminderTypeChange(e) {
      this.reminderTypeIndex = e.detail.value
      this.formData.type = this.reminderTypeOptions[this.reminderTypeIndex].value
    },
    
    onDateChange(e) {
      this.formData.date = e.detail.value
    },
    
    onTimeChange(e) {
      this.formData.time = e.detail.value
    },
    
    onLunarDayChange(e) {
      this.lunarDayIndex = e.detail.value
      this.formData.lunarDay = parseInt(e.detail.value) + 1
    },
    
    // 返回万年历
    goBack() {
      uni.navigateBack({
        delta: 1
      })
    }
  }
}
</script>

<style scoped>
.reminders-container {
  min-height: 100vh;
  background: #F5F5F5;
  padding: 40rpx;
}

/* 头部样式 */
.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.subtitle {
  display: block;
  font-size: 24rpx;
  color: #666;
}

/* 添加按钮 */
.add-section {
  margin-bottom: 40rpx;
}

.add-btn {
  width: 100%;
  height: 100rpx;
  background: linear-gradient(135deg, #FF0000 0%, #CC0000 100%);
  color: white;
  border: none;
  border-radius: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 15rpx rgba(255, 0, 0, 0.3);
}

.add-btn:active {
  transform: scale(0.98);
}

.add-icon {
  font-size: 36rpx;
  margin-right: 15rpx;
}

.add-text {
  font-size: 32rpx;
  font-weight: bold;
}

/* 提醒列表 */
.reminders-list {
  margin-bottom: 40rpx;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.list-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.cleanup-btn {
  background: #FFF3E0;
  color: #FF9800;
  border: 2rpx solid #FF9800;
  border-radius: 30rpx;
  padding: 10rpx 20rpx;
  font-size: 24rpx;
}

.reminder-item {
  background: white;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.reminder-item.disabled {
  opacity: 0.6;
}

.reminder-item.expired {
  background: #F5F5F5;
}

.reminder-main {
  display: flex;
  align-items: center;
  padding: 30rpx;
}

.reminder-info {
  flex: 1;
}

.reminder-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.reminder-content {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.reminder-details {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.reminder-time {
  font-size: 24rpx;
  color: #999;
}

.reminder-type {
  font-size: 24rpx;
  color: #FF0000;
  background: #FFE5E5;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.reminder-status {
  
}

.reminder-actions {
  display: flex;
  border-top: 2rpx solid #F0F0F0;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border: none;
  font-size: 28rpx;
}

.edit-btn {
  background: #E3F2FD;
  color: #2196F3;
}

.delete-btn {
  background: #FFEBEE;
  color: #F44336;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
}

.empty-icon {
  display: block;
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.empty-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #666;
}

/* 快速模板 */
.quick-templates {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.templates-title {
  text-align: center;
  margin-bottom: 30rpx;
}

.templates-title text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.templates-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.template-item {
  background: #F8F9FA;
  border: 2rpx solid #E9ECEF;
  border-radius: 15rpx;
  padding: 30rpx;
  text-align: center;
}

.template-item:active {
  background: #E9ECEF;
}

.template-icon {
  display: block;
  font-size: 48rpx;
  margin-bottom: 15rpx;
}

.template-name {
  font-size: 26rpx;
  color: #333;
}

/* 弹窗样式 */
.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  z-index: 1000;
}

.popup-content {
  width: 100%;
  background: white;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 80vh;
  overflow-y: auto;
}

/* 表单样式 */
.reminder-form {
  background: white;
  border-radius: 20rpx 20rpx 0 0;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx;
  border-bottom: 2rpx solid #F0F0F0;
}

.form-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.form-close {
  width: 60rpx;
  height: 60rpx;
  background: #F0F0F0;
  border: none;
  border-radius: 50%;
  font-size: 36rpx;
  color: #666;
}

.form-content {
  padding: 40rpx;
}

.form-item {
  margin-bottom: 40rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
}

.form-input, .form-textarea {
  width: 100%;
  padding: 20rpx;
  border: 2rpx solid #E0E0E0;
  border-radius: 10rpx;
  font-size: 28rpx;
  background: #FAFAFA;
}

.form-textarea {
  height: 120rpx;
  resize: none;
}

.form-picker {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border: 2rpx solid #E0E0E0;
  border-radius: 10rpx;
  background: #FAFAFA;
}

.picker-arrow {
  color: #999;
  font-size: 24rpx;
}

.form-actions {
  display: flex;
  padding: 40rpx;
  border-top: 2rpx solid #F0F0F0;
  gap: 20rpx;
}

.form-btn {
  flex: 1;
  height: 80rpx;
  border: none;
  border-radius: 40rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.cancel-btn {
  background: #F0F0F0;
  color: #666;
}

.confirm-btn {
  background: #FF0000;
  color: white;
}

/* 返回按钮 */
.back-section {
  text-align: center;
  margin-top: 40rpx;
}

.back-btn {
  width: 100%;
  height: 100rpx;
  background: #FF0000;
  color: white;
  border: none;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.back-btn:active {
  background: #CC0000;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-text {
  background: white;
  padding: 30rpx 40rpx;
  border-radius: 15rpx;
  font-size: 28rpx;
  color: #333;
}
</style>
