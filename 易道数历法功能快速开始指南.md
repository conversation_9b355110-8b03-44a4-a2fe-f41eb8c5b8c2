# 易道数历法功能快速开始指南

## 立即开始

### 第一步：安装依赖

在项目根目录执行：

```bash
npm install lunar-javascript
```

验证安装：
```bash
node -e "const {Solar} = require('lunar-javascript'); console.log(Solar.fromYmd(2024,1,1).toFullString())"
```

### 第二步：创建历法工具类

创建文件 `src/utils/calendar.js`：

```javascript
import { Solar, Lunar } from 'lunar-javascript'

export class CalendarUtil {
  // 获取指定日期的完整历法信息
  static getCalendarInfo(date = new Date()) {
    const solar = Solar.fromDate(date)
    const lunar = solar.getLunar()
    
    return {
      solar: {
        year: solar.getYear(),
        month: solar.getMonth(),
        day: solar.getDay(),
        weekday: solar.getWeek()
      },
      lunar: {
        year: lunar.getYear(),
        month: lunar.getMonth(),
        day: lunar.getDay(),
        yearInChinese: lunar.getYearInChinese(),
        monthInChinese: lunar.getMonthInChinese(),
        dayInChinese: lunar.getDayInChinese()
      },
      ganzhi: {
        year: lunar.getYearInGanZhi(),
        month: lunar.getMonthInGanZhi(),
        day: lunar.getDayInGanZhi()
      },
      zodiac: lunar.getYearShengXiao(),
      jieqi: solar.getJieQi(),
      yi: lunar.getDayYi(),
      ji: lunar.getDayJi(),
      shenwei: {
        xi: lunar.getDayPositionXi(),
        cai: lunar.getDayPositionCai(),
        fu: lunar.getDayPositionFu()
      }
    }
  }

  // 佛教节日数据
  static BUDDHIST_FESTIVALS = {
    '农历四月初八': '佛诞日',
    '农历二月十九': '观音诞',
    '农历六月十九': '观音成道日',
    '农历九月十九': '观音出家日',
    '农历七月三十': '地藏诞'
  }
  
  // 道教节日数据
  static TAOIST_FESTIVALS = {
    '农历正月初九': '玉皇大帝诞',
    '农历二月十五': '太上老君诞',
    '农历七月十五': '中元节',
    '农历九月初九': '重阳节'
  }

  // 获取节日信息
  static getFestival(lunar) {
    const lunarDate = `农历${lunar.getMonthInChinese()}${lunar.getDayInChinese()}`
    return this.BUDDHIST_FESTIVALS[lunarDate] || this.TAOIST_FESTIVALS[lunarDate] || null
  }
}
```

### 第三步：扩展存储工具类

在 `src/utils/storage.js` 中添加：

```javascript
// 在现有StorageUtil类中添加以下方法

// 历法设置相关
static getCalendarSettings() {
  return this.getItem('calendar_settings', {
    showLunar: true,
    showBuddhist: true,
    showTaoist: true,
    reminderEnabled: true
  })
}

static saveCalendarSettings(settings) {
  this.setItem('calendar_settings', settings)
}

// 提醒相关
static getReminders() {
  return this.getItem('calendar_reminders', [])
}

static saveReminder(reminder) {
  const reminders = this.getReminders()
  reminders.push(reminder)
  this.setItem('calendar_reminders', reminders)
}
```

### 第四步：创建万年历主页面

创建目录和文件：
```
src/pages/calendar/
├── calendar.vue
```

`src/pages/calendar/calendar.vue`：

```vue
<template>
  <view class="calendar-container">
    <!-- 头部标题 -->
    <view class="header">
      <text class="title">中华万年历</text>
    </view>

    <!-- 今日信息卡片 -->
    <view class="today-card">
      <view class="solar-date">
        {{ todayInfo.solar?.year }}年{{ todayInfo.solar?.month }}月{{ todayInfo.solar?.day }}日
        {{ todayInfo.solar?.weekday }}
      </view>
      <view class="lunar-date">
        {{ todayInfo.lunar?.yearInChinese }}年{{ todayInfo.lunar?.monthInChinese }}{{ todayInfo.lunar?.dayInChinese }}
      </view>
      <view class="ganzhi">
        {{ todayInfo.ganzhi?.year }}年 {{ todayInfo.ganzhi?.month }}月 {{ todayInfo.ganzhi?.day }}日
      </view>
      <view class="zodiac">生肖：{{ todayInfo.zodiac }}</view>
    </view>

    <!-- 黄历信息 -->
    <view class="huangli-card">
      <view class="yi-section">
        <text class="label">宜：</text>
        <text class="content">{{ yiText }}</text>
      </view>
      <view class="ji-section">
        <text class="label">忌：</text>
        <text class="content">{{ jiText }}</text>
      </view>
    </view>

    <!-- 神位信息 -->
    <view class="shenwei-card">
      <view class="shenwei-item">
        <text class="label">喜神：</text>
        <text class="value">{{ todayInfo.shenwei?.xi }}</text>
      </view>
      <view class="shenwei-item">
        <text class="label">财神：</text>
        <text class="value">{{ todayInfo.shenwei?.cai }}</text>
      </view>
      <view class="shenwei-item">
        <text class="label">福神：</text>
        <text class="value">{{ todayInfo.shenwei?.fu }}</text>
      </view>
    </view>

    <!-- 功能按钮 -->
    <view class="function-buttons">
      <button class="function-btn" @click="goToBuddhist">
        <text>佛历</text>
      </button>
      <button class="function-btn" @click="goToTaoist">
        <text>道历</text>
      </button>
      <button class="function-btn" @click="goToReminders">
        <text>提醒</text>
      </button>
    </view>
  </view>
</template>

<script>
import { CalendarUtil } from '@/utils/calendar.js'
import { AuthUtil } from '@/utils/auth.js'

export default {
  data() {
    return {
      todayInfo: {}
    }
  },
  
  computed: {
    yiText() {
      return this.todayInfo.yi ? this.todayInfo.yi.join(' ') : '无'
    },
    jiText() {
      return this.todayInfo.ji ? this.todayInfo.ji.join(' ') : '无'
    }
  },
  
  onLoad() {
    AuthUtil.requireAuth()
    this.loadTodayInfo()
  },
  
  methods: {
    loadTodayInfo() {
      try {
        this.todayInfo = CalendarUtil.getCalendarInfo(new Date())
        console.log('今日历法信息:', this.todayInfo)
      } catch (error) {
        console.error('加载历法信息失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      }
    },
    
    goToBuddhist() {
      uni.navigateTo({
        url: '/pages/calendar/buddhist'
      })
    },
    
    goToTaoist() {
      uni.navigateTo({
        url: '/pages/calendar/taoist'
      })
    },
    
    goToReminders() {
      uni.navigateTo({
        url: '/pages/calendar/reminders'
      })
    }
  }
}
</script>

<style scoped>
.calendar-container {
  min-height: 100vh;
  background: #F5F5F5;
  padding: 40rpx;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
}

.today-card, .huangli-card, .shenwei-card {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.solar-date {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.lunar-date {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.ganzhi, .zodiac {
  font-size: 28rpx;
  color: #888;
  margin-bottom: 10rpx;
}

.yi-section, .ji-section {
  display: flex;
  margin-bottom: 20rpx;
}

.label {
  font-weight: bold;
  color: #333;
  width: 80rpx;
}

.content {
  flex: 1;
  color: #666;
}

.shenwei-card {
  display: flex;
  justify-content: space-between;
}

.shenwei-item {
  text-align: center;
}

.shenwei-item .label {
  display: block;
  font-size: 24rpx;
  color: #888;
  margin-bottom: 10rpx;
}

.shenwei-item .value {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.function-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 40rpx;
}

.function-btn {
  flex: 1;
  margin: 0 10rpx;
  background: #FF0000;
  color: white;
  border: none;
  border-radius: 15rpx;
  padding: 30rpx 0;
  font-size: 32rpx;
}

.function-btn:active {
  background: #CC0000;
}
</style>
```

### 第五步：更新页面配置

在 `src/pages.json` 的 `pages` 数组中添加：

```json
{
  "path": "pages/calendar/calendar",
  "style": {
    "navigationBarTitleText": "中华万年历",
    "navigationBarBackgroundColor": "#FF0000",
    "navigationBarTextStyle": "white"
  }
}
```

### 第六步：在主页面添加入口

在 `src/pages/home/<USER>

```vue
<!-- 在现有mode-item后添加 -->
<view class="mode-item" @click="navigateToCalendar">
  <view class="mode-icon">
    <image src="/src/static/calendar.png" class="mode-icon-image"></image>
  </view>
  <view class="mode-info">
    <text class="mode-title">中华万年历</text>
  </view>
  <view class="arrow">></view>
</view>
```

在methods中添加：

```javascript
navigateToCalendar() {
  uni.navigateTo({
    url: '/pages/calendar/calendar'
  })
}
```

### 第七步：测试基础功能

1. 运行项目：
```bash
npm run dev:h5
```

2. 在主页面点击"中华万年历"按钮
3. 查看是否正确显示今日历法信息
4. 检查控制台是否有错误信息

## 常见问题解决

### 问题1：lunar-javascript导入失败
**解决方案：**
```javascript
// 如果ES6导入失败，尝试CommonJS方式
const { Solar, Lunar } = require('lunar-javascript')
```

### 问题2：某些平台显示异常
**解决方案：**
- 检查uni-app兼容性
- 添加平台特定的条件编译
- 使用try-catch包装关键代码

### 问题3：性能问题
**解决方案：**
- 添加数据缓存
- 使用computed属性
- 避免频繁的日期计算

## 下一步开发

完成基础功能后，可以继续开发：

1. **佛历页面** (`src/pages/calendar/buddhist.vue`)
2. **道历页面** (`src/pages/calendar/taoist.vue`)
3. **提醒功能** (`src/pages/calendar/reminders.vue`)
4. **日历网格视图**
5. **定时提醒功能**

## 开发建议

1. **渐进式开发**：先实现基础功能，再逐步添加高级特性
2. **充分测试**：在不同平台上测试功能兼容性
3. **用户体验**：保持与现有应用的一致性
4. **性能优化**：合理使用缓存和异步加载
5. **错误处理**：添加完善的错误处理机制

按照这个快速开始指南，您可以在1-2小时内搭建起历法功能的基础框架，然后根据需要逐步完善各项功能。
