<template>
  <view class="mode-container">
 

    <view class="content">
      <!-- 结果显示区域 -->
      <view class="result-section" v-show="showResult && !isInputFocused">
        <view class="result-time" @click="copyResult">{{ currentResult }}</view>
        <view class="result-text">{{ currentText }}</view>
        <view class="result-timestamp">{{ currentTime }}</view>
      </view>

      <!-- 输入区域 -->
      <view class="input-section">
        
        <textarea 
          class="text-input"
          v-model="inputText"
          placeholder="请输入文字（最多50个字）"
          maxlength="50"
          :show-confirm-bar="false"
          @focus="handleInputFocus"
          @blur="handleInputBlur"
        />
        <view class="char-count">{{ inputText.length }}/50</view>
      </view>

      <!-- 确定按钮 -->
      <view class="button-section">
        
        <button class="confirm-btn" @click="generateTime" :disabled="!inputText.trim()">
          确定
        </button>
      </view>

      <!-- 历史记录按钮 -->
      <view class="history-section">
        <button class="history-btn" @click="viewHistory">
          查看梅花易数记录
        </button>
      </view>
    </view>
  </view>
</template>

<script>
import { AuthUtil } from '@/utils/auth.js'
import { RandomUtil } from '@/utils/random.js'
import { StorageUtil } from '@/utils/storage.js'

export default {
  data() {
    return {
      inputText: '',
      showResult: false,
      currentResult: '',
      currentText: '',
      currentTime: '',
      isInputFocused: false
    }
  },

  onLoad() {
    AuthUtil.requireAuth()
  },

  methods: {
    generateTime() {
      if (!this.inputText.trim()) {
        uni.showToast({
          title: '请输入文字',
          icon: 'none'
        })
        return
      }

      // 显示加载提示


      // 延时1.5秒后生成随机时间
      setTimeout(() => {
        // 生成随机时间
        const result = RandomUtil.generateMode2()
        const timestamp = RandomUtil.getCurrentTimestamp()
        
        // 显示结果
        this.currentResult = result
        this.currentText = this.inputText.trim()
        this.currentTime = timestamp
        this.showResult = true

        // 保存记录
        const record = RandomUtil.createRecord('mode2', this.currentText, result)
        StorageUtil.saveRecord(record)

        // 清空输入
        this.inputText = ''

        // 隐藏加载提示并显示成功提示
        uni.hideLoading()

      }, 1500)
    },

    viewHistory() {
      uni.navigateTo({
        url: '/pages/mode-records/mode-records?mode=mode2&title=梅花易数记录'
      })
    },

    handleInputFocus() {
      this.isInputFocused = true
    },

    handleInputBlur() {
      this.isInputFocused = false
    },

    // 复制结果到剪贴板
    copyResult() {
      if (this.currentText) {
        uni.setClipboardData({
          data: this.currentText.toString(),
          fail: () => {
            uni.showToast({
              title: '复制失败',
              icon: 'none',
              duration: 1500
            })
          }
        })
      }
    }
  }
}
</script>

<style scoped>
.mode-container {
  min-height: 100vh;
  background: #F5F5F5;
  padding: 40rpx;
}

.header {
  text-align: center;
  margin-bottom: 60rpx;
}

.mode-title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.mode-desc {
  display: block;
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

.content {
  max-width: 600rpx;
  margin: 0 auto;
}

.result-section {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx 40rpx 30rpx 40rpx;
  text-align: center;
  margin-bottom: 60rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.result-time {
  font-size: 160rpx;
  font-weight: bold;
  color: #FF0000;
  margin-bottom: 30rpx;
  margin-top: 40rpx;
  cursor: pointer;
  user-select: none;
  transition: opacity 0.2s ease;
}

.result-time:active {
  opacity: 0.7;
}

.result-text {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 20rpx;
  word-break: break-all;
}

.result-timestamp {
  font-size: 24rpx;
  color: #999;
}

.input-section {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.step-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.text-input {
  width: 100%;
  min-height: 120rpx;
  background: #F8F8F8;
  border-radius: 10rpx;
  padding: 20rpx;
  font-size: 40rpx;
  box-sizing: border-box;
  margin-bottom: 10rpx;
}

.char-count {
  text-align: right;
  font-size: 24rpx;
  color: #999;
}

.button-section {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.confirm-btn {
  width: 100%;
  height: 100rpx;
  background: #FF0000;
  color: white;
  border: none;
  border-radius: 50rpx;
  font-size: 36rpx;
  font-weight: bold;
}

.confirm-btn:disabled {
  background: #ccc;
}

.history-section {
  text-align: center;
}

.history-btn {
  width: 100%;
  height: 80rpx;
  background: transparent;
  color: #FF0000;
  border: 2rpx solid #FF0000;
  border-radius: 40rpx;
  font-size: 30rpx;
}
</style>
