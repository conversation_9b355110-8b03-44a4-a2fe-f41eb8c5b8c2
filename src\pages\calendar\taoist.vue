<template>
  <view class="taoist-container">
    <!-- 头部标题 -->
    <view class="header">
      <text class="title">道历</text>
      <text class="subtitle">道法自然 · 养生修炼</text>
    </view>

    <!-- 道历日期卡片 -->
    <view class="date-card">
      <view class="taoist-date-section">
        <view class="taoist-year">
          <text class="year-label">道历</text>
          <text class="year-value">黄帝纪年{{ taoistYear }}年</text>
        </view>
        <view class="solar-date">
          <text>公元{{ todayInfo.solar?.year }}年{{ todayInfo.solar?.month }}月{{ todayInfo.solar?.day }}日</text>
        </view>
        <view class="lunar-date">
          <text>农历{{ todayInfo.lunar?.yearInChinese }}年{{ todayInfo.lunar?.monthInChinese }}{{ todayInfo.lunar?.dayInChinese }}</text>
        </view>
        <view class="ganzhi-date">
          <text>{{ todayInfo.ganzhi?.year }}年 {{ todayInfo.ganzhi?.month }}月 {{ todayInfo.ganzhi?.day }}日</text>
        </view>
      </view>
    </view>

    <!-- 道教节日卡片 -->
    <view class="festival-card" v-if="taoistFestivals.length > 0">
      <view class="card-title">
        <text>☯ 道教节日</text>
      </view>
      <view class="festival-list">
        <view 
          v-for="(festival, index) in taoistFestivals" 
          :key="index"
          class="festival-item"
        >
          <text class="festival-name">{{ festival.name }}</text>
        </view>
      </view>
    </view>

    <!-- 修炼吉日卡片 -->
    <view class="lucky-card">
      <view class="card-title">
        <text>🌟 修炼吉日</text>
      </view>
      <view class="lucky-content">
        <view v-if="taoistInfo.luckyDays" class="lucky-today">
          <text class="lucky-text">{{ taoistInfo.luckyDays }}</text>
          <text class="lucky-desc">天地清明，宜静心修炼</text>
        </view>
        <view v-else class="lucky-normal">
          <text class="lucky-text">今日宜顺应自然</text>
          <text class="lucky-desc">虽非特殊吉日，亦可修身养性</text>
        </view>
        
        <!-- 修炼指导 -->
        <view class="lucky-guide">
          <text class="guide-title">修炼要诀：</text>
          <text class="guide-text">清静无为，顺应天道，内观自省，炼精化气</text>
        </view>
      </view>
    </view>

    <!-- 养生时辰卡片 -->
    <view class="yangsheng-card">
      <view class="card-title">
        <text>⏰ 养生时辰</text>
      </view>
      <view class="yangsheng-content">
        <view class="current-time">
          <text class="time-label">当前时辰建议：</text>
          <text class="time-advice">{{ taoistInfo.yangsheng }}</text>
        </view>
        
        <!-- 十二时辰养生 -->
        <view class="time-schedule">
          <view class="schedule-title">
            <text>十二时辰养生法：</text>
          </view>
          <view class="schedule-grid">
            <view class="schedule-item" v-for="(item, index) in timeSchedule" :key="index">
              <text class="schedule-time">{{ item.time }}</text>
              <text class="schedule-desc">{{ item.desc }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 道教智慧卡片 -->
    <view class="wisdom-card">
      <view class="card-title">
        <text>💡 道教智慧</text>
      </view>
      <view class="wisdom-content">
        <view class="wisdom-quote">
          <text class="quote-text">"道法自然，无为而治"</text>
          <text class="quote-author">—— 《道德经》</text>
        </view>
        
        <view class="wisdom-items">
          <view class="wisdom-item">
            <text class="wisdom-title">三宝：</text>
            <text class="wisdom-text">慈（慈爱）、俭（节俭）、不敢为天下先（谦逊）</text>
          </view>
          <view class="wisdom-item">
            <text class="wisdom-title">五行：</text>
            <text class="wisdom-text">金、木、水、火、土，相生相克，循环不息</text>
          </view>
          <view class="wisdom-item">
            <text class="wisdom-title">阴阳：</text>
            <text class="wisdom-text">阴阳调和，刚柔并济，万物生长</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 返回按钮 -->
    <view class="back-section">
      <button class="back-btn" @click="goBack">
        返回万年历
      </button>
    </view>

    <!-- 加载状态 -->
    <view v-if="loading" class="loading-overlay">
      <text class="loading-text">正在加载道历信息...</text>
    </view>
  </view>
</template>

<script>
import { CalendarUtil } from '@/utils/calendar.js'
import { StorageUtil } from '@/utils/storage.js'
import { AuthUtil } from '@/utils/auth.js'

export default {
  data() {
    return {
      todayInfo: {},
      taoistInfo: {},
      loading: true,
      timeSchedule: [
        { time: '子时(23-01)', desc: '宜静坐调息' },
        { time: '丑时(01-03)', desc: '宜安眠休息' },
        { time: '寅时(03-05)', desc: '宜深度睡眠' },
        { time: '卯时(05-07)', desc: '宜起床活动' },
        { time: '辰时(07-09)', desc: '宜进食早餐' },
        { time: '巳时(09-11)', desc: '宜工作学习' },
        { time: '午时(11-13)', desc: '宜小憩片刻' },
        { time: '未时(13-15)', desc: '宜午休调养' },
        { time: '申时(15-17)', desc: '宜适度运动' },
        { time: '酉时(17-19)', desc: '宜收敛精神' },
        { time: '戌时(19-21)', desc: '宜放松身心' },
        { time: '亥时(21-23)', desc: '宜准备就寝' }
      ]
    }
  },
  
  computed: {
    // 道教节日列表
    taoistFestivals() {
      if (!this.todayInfo.festivals) return []
      return this.todayInfo.festivals.filter(f => f.type === 'taoist')
    },
    
    // 道历年份（以黄帝纪年计算，约公元前2697年）
    taoistYear() {
      if (!this.todayInfo.solar) return 0
      return this.todayInfo.solar.year + 2697
    }
  },
  
  onLoad() {
    AuthUtil.requireAuth()
    this.loadTaoistInfo()
  },
  
  onShow() {
    this.loadTaoistInfo()
  },
  
  methods: {
    // 加载道历信息
    async loadTaoistInfo() {
      try {
        this.loading = true
        
        const today = new Date()
        const dateKey = `taoist_${today.getFullYear()}-${today.getMonth() + 1}-${today.getDate()}`
        
        // 尝试从缓存获取
        let cachedData = StorageUtil.getCachedCalendarData(dateKey)
        
        if (cachedData) {
          this.todayInfo = cachedData.todayInfo
          this.taoistInfo = cachedData.taoistInfo
        } else {
          // 获取今日历法信息
          const calendarInfo = CalendarUtil.getCalendarInfo(today)
          const taoistInfo = CalendarUtil.getTaoistInfo(today)
          
          if (calendarInfo && taoistInfo) {
            this.todayInfo = calendarInfo
            this.taoistInfo = taoistInfo
            
            // 缓存数据
            StorageUtil.setCachedCalendarData(dateKey, {
              todayInfo: calendarInfo,
              taoistInfo: taoistInfo
            })
            
            // 保存查看记录
            StorageUtil.saveCalendarViewRecord(today, 'taoist')
          } else {
            throw new Error('获取道历信息失败')
          }
        }
        
        console.log('道历信息:', this.taoistInfo)
        
      } catch (error) {
        console.error('加载道历信息失败:', error)
        uni.showToast({
          title: '加载失败，请重试',
          icon: 'none',
          duration: 2000
        })
      } finally {
        this.loading = false
      }
    },
    
    // 返回万年历
    goBack() {
      uni.navigateBack({
        delta: 1
      })
    }
  }
}
</script>

<style scoped>
.taoist-container {
  min-height: 100vh;
  background: #F5F5F5;
  padding: 40rpx;
}

/* 头部样式 */
.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.subtitle {
  display: block;
  font-size: 24rpx;
  color: #666;
}

/* 卡片通用样式 */
.date-card, .festival-card, .lucky-card, .yangsheng-card, .wisdom-card {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

/* 道历日期卡片 */
.taoist-date-section {
  text-align: center;
}

.taoist-year {
  margin-bottom: 20rpx;
}

.year-label {
  font-size: 28rpx;
  color: #666;
  margin-right: 15rpx;
}

.year-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #9C27B0;
}

.solar-date, .lunar-date, .ganzhi-date {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
}

/* 卡片标题 */
.card-title {
  text-align: center;
  margin-bottom: 30rpx;
}

.card-title text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

/* 道教节日 */
.festival-list {
  
}

.festival-item {
  background: #F3E5F5;
  border-left: 4rpx solid #9C27B0;
  padding: 20rpx;
  margin-bottom: 15rpx;
  border-radius: 10rpx;
}

.festival-item:last-child {
  margin-bottom: 0;
}

.festival-name {
  font-size: 28rpx;
  color: #9C27B0;
  font-weight: bold;
}

/* 修炼吉日 */
.lucky-content {
  
}

.lucky-today, .lucky-normal {
  text-align: center;
  padding: 30rpx;
  border-radius: 15rpx;
  margin-bottom: 25rpx;
}

.lucky-today {
  background: #E8F5E8;
  border: 2rpx solid #4CAF50;
}

.lucky-normal {
  background: #F0F0F0;
  border: 2rpx solid #999;
}

.lucky-text {
  display: block;
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.lucky-desc {
  display: block;
  font-size: 24rpx;
  color: #666;
}

.lucky-guide {
  background: #F8F9FA;
  padding: 20rpx;
  border-radius: 10rpx;
}

.guide-title {
  display: block;
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.guide-text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.6;
}

/* 养生时辰 */
.yangsheng-content {
  
}

.current-time {
  text-align: center;
  background: #E3F2FD;
  border: 2rpx solid #2196F3;
  border-radius: 15rpx;
  padding: 30rpx;
  margin-bottom: 25rpx;
}

.time-label {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.time-advice {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #1976D2;
}

.time-schedule {
  
}

.schedule-title {
  margin-bottom: 20rpx;
}

.schedule-title text {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
}

.schedule-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15rpx;
}

.schedule-item {
  background: #F8F9FA;
  padding: 15rpx;
  border-radius: 10rpx;
  border-left: 3rpx solid #2196F3;
}

.schedule-time {
  display: block;
  font-size: 22rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 5rpx;
}

.schedule-desc {
  font-size: 20rpx;
  color: #666;
}

/* 道教智慧 */
.wisdom-content {
  
}

.wisdom-quote {
  text-align: center;
  background: #FFF3E0;
  border: 2rpx solid #FF9800;
  border-radius: 15rpx;
  padding: 30rpx;
  margin-bottom: 25rpx;
}

.quote-text {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #E65100;
  margin-bottom: 10rpx;
}

.quote-author {
  font-size: 22rpx;
  color: #666;
}

.wisdom-items {
  
}

.wisdom-item {
  margin-bottom: 20rpx;
}

.wisdom-item:last-child {
  margin-bottom: 0;
}

.wisdom-title {
  display: block;
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.wisdom-text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.6;
}

/* 返回按钮 */
.back-section {
  text-align: center;
  margin-top: 40rpx;
}

.back-btn {
  width: 100%;
  height: 100rpx;
  background: #FF0000;
  color: white;
  border: none;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.back-btn:active {
  background: #CC0000;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-text {
  background: white;
  padding: 30rpx 40rpx;
  border-radius: 15rpx;
  font-size: 28rpx;
  color: #333;
}
</style>
