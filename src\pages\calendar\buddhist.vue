<template>
  <view class="buddhist-container">
    <!-- 头部标题 -->
    <view class="header">
      <text class="title">佛历</text>
      <text class="subtitle">慈悲智慧 · 修行指导</text>
    </view>

    <!-- 佛历日期卡片 -->
    <view class="date-card">
      <view class="buddhist-date-section">
        <view class="buddhist-year">
          <text class="year-label">佛历</text>
          <text class="year-value">{{ buddhistInfo.year }}年</text>
        </view>
        <view class="solar-date">
          <text>公元{{ todayInfo.solar?.year }}年{{ todayInfo.solar?.month }}月{{ todayInfo.solar?.day }}日</text>
        </view>
        <view class="lunar-date">
          <text>农历{{ todayInfo.lunar?.yearInChinese }}年{{ todayInfo.lunar?.monthInChinese }}{{ todayInfo.lunar?.dayInChinese }}</text>
        </view>
      </view>
    </view>

    <!-- 佛教节日卡片 -->
    <view class="festival-card" v-if="buddhistFestivals.length > 0">
      <view class="card-title">
        <text>🙏 佛教节日</text>
      </view>
      <view class="festival-list">
        <view 
          v-for="(festival, index) in buddhistFestivals" 
          :key="index"
          class="festival-item"
        >
          <text class="festival-name">{{ festival.name }}</text>
        </view>
      </view>
    </view>

    <!-- 斋戒日信息卡片 -->
    <view class="zhai-card">
      <view class="card-title">
        <text>🌙 斋戒日信息</text>
      </view>
      <view class="zhai-content">
        <view v-if="buddhistInfo.zhaiDays" class="zhai-today">
          <text class="zhai-text">{{ buddhistInfo.zhaiDays }}</text>
          <text class="zhai-desc">宜持戒念佛，清净身心</text>
        </view>
        <view v-else class="zhai-normal">
          <text class="zhai-text">今日非斋戒日</text>
          <text class="zhai-desc">可正常饮食，但仍宜慈悲为怀</text>
        </view>
        
        <!-- 斋戒日说明 -->
        <view class="zhai-info">
          <text class="info-title">斋戒日说明：</text>
          <text class="info-text">农历每月初一、初八、十四、十五、十八、廿三、廿四、廿八、廿九、三十为斋戒日</text>
        </view>
      </view>
    </view>

    <!-- 修行建议卡片 -->
    <view class="practice-card">
      <view class="card-title">
        <text>📿 今日修行</text>
      </view>
      <view class="practice-content">
        <view class="practice-main">
          <text class="practice-text">{{ buddhistInfo.practice }}</text>
        </view>
        
        <!-- 修行指导 -->
        <view class="practice-guide">
          <view class="guide-item">
            <text class="guide-label">晨课：</text>
            <text class="guide-text">念诵《心经》、《大悲咒》</text>
          </view>
          <view class="guide-item">
            <text class="guide-label">日中：</text>
            <text class="guide-text">持念佛号，观照内心</text>
          </view>
          <view class="guide-item">
            <text class="guide-label">晚课：</text>
            <text class="guide-text">诵经回向，忏悔业障</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 佛教常识卡片 -->
    <view class="knowledge-card">
      <view class="card-title">
        <text>💡 佛教常识</text>
      </view>
      <view class="knowledge-content">
        <view class="knowledge-item">
          <text class="knowledge-title">佛历起源：</text>
          <text class="knowledge-text">以释迦牟尼佛涅槃年为元年，比公历早543年</text>
        </view>
        <view class="knowledge-item">
          <text class="knowledge-title">三宝：</text>
          <text class="knowledge-text">佛（觉悟者）、法（教法）、僧（僧团）</text>
        </view>
        <view class="knowledge-item">
          <text class="knowledge-title">五戒：</text>
          <text class="knowledge-text">不杀生、不偷盗、不邪淫、不妄语、不饮酒</text>
        </view>
      </view>
    </view>

    <!-- 返回按钮 -->
    <view class="back-section">
      <button class="back-btn" @click="goBack">
        返回万年历
      </button>
    </view>

    <!-- 加载状态 -->
    <view v-if="loading" class="loading-overlay">
      <text class="loading-text">正在加载佛历信息...</text>
    </view>
  </view>
</template>

<script>
import { CalendarUtil } from '@/utils/calendar.js'
import { StorageUtil } from '@/utils/storage.js'
import { AuthUtil } from '@/utils/auth.js'

export default {
  data() {
    return {
      todayInfo: {},
      buddhistInfo: {},
      loading: true
    }
  },
  
  computed: {
    // 佛教节日列表
    buddhistFestivals() {
      if (!this.todayInfo.festivals) return []
      return this.todayInfo.festivals.filter(f => f.type === 'buddhist')
    }
  },
  
  onLoad() {
    AuthUtil.requireAuth()
    this.loadBuddhistInfo()
  },
  
  onShow() {
    this.loadBuddhistInfo()
  },
  
  methods: {
    // 加载佛历信息
    async loadBuddhistInfo() {
      try {
        this.loading = true
        
        const today = new Date()
        const dateKey = `buddhist_${today.getFullYear()}-${today.getMonth() + 1}-${today.getDate()}`
        
        // 尝试从缓存获取
        let cachedData = StorageUtil.getCachedCalendarData(dateKey)
        
        if (cachedData) {
          this.todayInfo = cachedData.todayInfo
          this.buddhistInfo = cachedData.buddhistInfo
        } else {
          // 获取今日历法信息
          const calendarInfo = CalendarUtil.getCalendarInfo(today)
          const buddhistInfo = CalendarUtil.getBuddhistInfo(today)
          
          if (calendarInfo && buddhistInfo) {
            this.todayInfo = calendarInfo
            this.buddhistInfo = buddhistInfo
            
            // 缓存数据
            StorageUtil.setCachedCalendarData(dateKey, {
              todayInfo: calendarInfo,
              buddhistInfo: buddhistInfo
            })
            
            // 保存查看记录
            StorageUtil.saveCalendarViewRecord(today, 'buddhist')
          } else {
            throw new Error('获取佛历信息失败')
          }
        }
        
        console.log('佛历信息:', this.buddhistInfo)
        
      } catch (error) {
        console.error('加载佛历信息失败:', error)
        uni.showToast({
          title: '加载失败，请重试',
          icon: 'none',
          duration: 2000
        })
      } finally {
        this.loading = false
      }
    },
    
    // 返回万年历
    goBack() {
      uni.navigateBack({
        delta: 1
      })
    }
  }
}
</script>

<style scoped>
.buddhist-container {
  min-height: 100vh;
  background: #F5F5F5;
  padding: 40rpx;
}

/* 头部样式 */
.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.subtitle {
  display: block;
  font-size: 24rpx;
  color: #666;
}

/* 卡片通用样式 */
.date-card, .festival-card, .zhai-card, .practice-card, .knowledge-card {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

/* 佛历日期卡片 */
.buddhist-date-section {
  text-align: center;
}

.buddhist-year {
  margin-bottom: 20rpx;
}

.year-label {
  font-size: 28rpx;
  color: #666;
  margin-right: 15rpx;
}

.year-value {
  font-size: 42rpx;
  font-weight: bold;
  color: #FF6B35;
}

.solar-date, .lunar-date {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
}

/* 卡片标题 */
.card-title {
  text-align: center;
  margin-bottom: 30rpx;
}

.card-title text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

/* 佛教节日 */
.festival-list {
  
}

.festival-item {
  background: #FFF3E0;
  border-left: 4rpx solid #FF6B35;
  padding: 20rpx;
  margin-bottom: 15rpx;
  border-radius: 10rpx;
}

.festival-item:last-child {
  margin-bottom: 0;
}

.festival-name {
  font-size: 28rpx;
  color: #FF6B35;
  font-weight: bold;
}

/* 斋戒日信息 */
.zhai-content {
  
}

.zhai-today, .zhai-normal {
  text-align: center;
  padding: 30rpx;
  border-radius: 15rpx;
  margin-bottom: 25rpx;
}

.zhai-today {
  background: #E8F5E8;
  border: 2rpx solid #4CAF50;
}

.zhai-normal {
  background: #F0F0F0;
  border: 2rpx solid #999;
}

.zhai-text {
  display: block;
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.zhai-desc {
  display: block;
  font-size: 24rpx;
  color: #666;
}

.zhai-info {
  background: #F8F9FA;
  padding: 20rpx;
  border-radius: 10rpx;
}

.info-title {
  display: block;
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.info-text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.6;
}

/* 修行建议 */
.practice-content {
  
}

.practice-main {
  text-align: center;
  background: #FFF8E1;
  border: 2rpx solid #FFC107;
  border-radius: 15rpx;
  padding: 30rpx;
  margin-bottom: 25rpx;
}

.practice-text {
  font-size: 28rpx;
  color: #F57C00;
  font-weight: bold;
  line-height: 1.6;
}

.practice-guide {
  
}

.guide-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 15rpx;
}

.guide-item:last-child {
  margin-bottom: 0;
}

.guide-label {
  font-size: 26rpx;
  font-weight: bold;
  color: #666;
  width: 120rpx;
  flex-shrink: 0;
}

.guide-text {
  font-size: 26rpx;
  color: #333;
  flex: 1;
}

/* 佛教常识 */
.knowledge-content {
  
}

.knowledge-item {
  margin-bottom: 20rpx;
}

.knowledge-item:last-child {
  margin-bottom: 0;
}

.knowledge-title {
  display: block;
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.knowledge-text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.6;
}

/* 返回按钮 */
.back-section {
  text-align: center;
  margin-top: 40rpx;
}

.back-btn {
  width: 100%;
  height: 100rpx;
  background: #FF0000;
  color: white;
  border: none;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.back-btn:active {
  background: #CC0000;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-text {
  background: white;
  padding: 30rpx 40rpx;
  border-radius: 15rpx;
  font-size: 28rpx;
  color: #333;
}
</style>
