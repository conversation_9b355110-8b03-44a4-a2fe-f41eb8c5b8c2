// 提醒工具类 - 集成uni-app本地通知API
import { StorageUtil } from './storage.js'
import { CalendarUtil } from './calendar.js'
import { Solar, Lunar } from 'lunar-javascript'

export class ReminderUtil {
  // 提醒类型枚举
  static REMINDER_TYPES = {
    ONCE: 'once',                    // 单次提醒
    LUNAR_MONTHLY: 'lunar_monthly',  // 农历月重复（如每月初一、十五）
    SOLAR_MONTHLY: 'solar_monthly',  // 公历月重复
    LUNAR_YEARLY: 'lunar_yearly',    // 农历年重复（节日）
    SOLAR_YEARLY: 'solar_yearly',    // 公历年重复
    WEEKLY: 'weekly',                // 每周重复
    DAILY: 'daily'                   // 每日重复
  }

  // 提醒状态枚举
  static REMINDER_STATUS = {
    ACTIVE: 'active',       // 激活状态
    PAUSED: 'paused',       // 暂停状态
    EXPIRED: 'expired',     // 已过期
    COMPLETED: 'completed'  // 已完成
  }

  // 生成唯一ID
  static generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }

  // 创建新提醒
  static createReminder(options) {
    try {
      const reminder = {
        id: this.generateId(),
        title: options.title || '历法提醒',
        content: options.content || '',
        type: options.type || this.REMINDER_TYPES.ONCE,
        date: options.date, // Date对象或日期字符串
        time: options.time, // 时间字符串 "HH:MM"
        lunarDay: options.lunarDay || null, // 农历日期（用于农历重复）
        enabled: true,
        status: this.REMINDER_STATUS.ACTIVE,
        createTime: Date.now(),
        lastTriggered: null,
        triggerCount: 0
      }

      // 验证提醒数据
      if (!this.validateReminder(reminder)) {
        throw new Error('提醒数据验证失败')
      }

      // 保存提醒
      const success = StorageUtil.saveReminder(reminder)
      if (!success) {
        throw new Error('保存提醒失败')
      }

      // 安排通知
      this.scheduleNotification(reminder)

      console.log('创建提醒成功:', reminder)
      return reminder

    } catch (error) {
      console.error('创建提醒失败:', error)
      throw error
    }
  }

  // 验证提醒数据
  static validateReminder(reminder) {
    if (!reminder.title || reminder.title.trim() === '') {
      return false
    }
    if (!reminder.date || !reminder.time) {
      return false
    }
    if (!Object.values(this.REMINDER_TYPES).includes(reminder.type)) {
      return false
    }
    return true
  }

  // 安排通知
  static scheduleNotification(reminder) {
    try {
      if (!reminder.enabled || reminder.status !== this.REMINDER_STATUS.ACTIVE) {
        return false
      }

      const nextTriggerTime = this.calculateNextTriggerTime(reminder)
      if (!nextTriggerTime || nextTriggerTime <= Date.now()) {
        return false
      }

      // 检查平台支持
      if (this.isPlatformSupported()) {
        this.createLocalNotification(reminder, nextTriggerTime)
      } else {
        console.warn('当前平台不支持本地通知，使用应用内提醒')
        this.scheduleInAppReminder(reminder, nextTriggerTime)
      }

      return true

    } catch (error) {
      console.error('安排通知失败:', error)
      return false
    }
  }

  // 计算下次触发时间
  static calculateNextTriggerTime(reminder) {
    try {
      const now = new Date()
      const [hours, minutes] = reminder.time.split(':').map(Number)
      
      let targetDate = new Date(reminder.date)
      targetDate.setHours(hours, minutes, 0, 0)

      // 根据提醒类型计算下次触发时间
      switch (reminder.type) {
        case this.REMINDER_TYPES.ONCE:
          return targetDate.getTime()

        case this.REMINDER_TYPES.LUNAR_MONTHLY:
          return this.getNextLunarMonthlyDate(reminder, now)

        case this.REMINDER_TYPES.SOLAR_MONTHLY:
          return this.getNextSolarMonthlyDate(reminder, now)

        case this.REMINDER_TYPES.LUNAR_YEARLY:
          return this.getNextLunarYearlyDate(reminder, now)

        case this.REMINDER_TYPES.SOLAR_YEARLY:
          return this.getNextSolarYearlyDate(reminder, now)

        case this.REMINDER_TYPES.WEEKLY:
          return this.getNextWeeklyDate(reminder, now)

        case this.REMINDER_TYPES.DAILY:
          return this.getNextDailyDate(reminder, now)

        default:
          return targetDate.getTime()
      }

    } catch (error) {
      console.error('计算触发时间失败:', error)
      return null
    }
  }

  // 获取下一个农历月重复日期
  static getNextLunarMonthlyDate(reminder, currentTime) {
    try {
      const [hours, minutes] = reminder.time.split(':').map(Number)
      
      // 如果有农历日期信息，使用它
      if (reminder.lunarDay) {
        const targetLunarDay = reminder.lunarDay
        let searchDate = new Date(currentTime)
        
        // 向前搜索最多60天，找到下一个匹配的农历日期
        for (let i = 0; i < 60; i++) {
          searchDate.setDate(searchDate.getDate() + 1)
          const solar = Solar.fromDate(searchDate)
          const lunar = solar.getLunar()
          
          if (lunar.getDay() === targetLunarDay) {
            searchDate.setHours(hours, minutes, 0, 0)
            if (searchDate > currentTime) {
              return searchDate.getTime()
            }
          }
        }
      }
      
      return null
    } catch (error) {
      console.error('计算农历月重复日期失败:', error)
      return null
    }
  }

  // 获取下一个公历月重复日期
  static getNextSolarMonthlyDate(reminder, currentTime) {
    const [hours, minutes] = reminder.time.split(':').map(Number)
    const originalDate = new Date(reminder.date)
    const targetDay = originalDate.getDate()
    
    let nextDate = new Date(currentTime)
    nextDate.setDate(targetDay)
    nextDate.setHours(hours, minutes, 0, 0)
    
    // 如果当月的日期已过，移到下个月
    if (nextDate <= currentTime) {
      nextDate.setMonth(nextDate.getMonth() + 1)
      nextDate.setDate(targetDay)
    }
    
    // 处理月末日期（如31号在2月不存在）
    if (nextDate.getDate() !== targetDay) {
      nextDate.setDate(0) // 设置为上个月的最后一天
    }
    
    return nextDate.getTime()
  }

  // 获取下一个农历年重复日期
  static getNextLunarYearlyDate(reminder, currentTime) {
    // 农历年重复主要用于传统节日
    const [hours, minutes] = reminder.time.split(':').map(Number)
    let searchDate = new Date(currentTime)
    
    // 向前搜索一年，找到下一个匹配的农历日期
    for (let i = 0; i < 400; i++) {
      searchDate.setDate(searchDate.getDate() + 1)
      const solar = Solar.fromDate(searchDate)
      const lunar = solar.getLunar()
      
      // 检查是否匹配农历月日
      if (reminder.lunarDay && lunar.getDay() === reminder.lunarDay) {
        searchDate.setHours(hours, minutes, 0, 0)
        if (searchDate > currentTime) {
          return searchDate.getTime()
        }
      }
    }
    
    return null
  }

  // 获取下一个公历年重复日期
  static getNextSolarYearlyDate(reminder, currentTime) {
    const [hours, minutes] = reminder.time.split(':').map(Number)
    const originalDate = new Date(reminder.date)
    
    let nextDate = new Date(currentTime.getFullYear(), originalDate.getMonth(), originalDate.getDate())
    nextDate.setHours(hours, minutes, 0, 0)
    
    // 如果今年的日期已过，移到明年
    if (nextDate <= currentTime) {
      nextDate.setFullYear(nextDate.getFullYear() + 1)
    }
    
    return nextDate.getTime()
  }

  // 获取下一个每周重复日期
  static getNextWeeklyDate(reminder, currentTime) {
    const [hours, minutes] = reminder.time.split(':').map(Number)
    const originalDate = new Date(reminder.date)
    const targetWeekday = originalDate.getDay()
    
    let nextDate = new Date(currentTime)
    const currentWeekday = nextDate.getDay()
    const daysUntilTarget = (targetWeekday - currentWeekday + 7) % 7
    
    if (daysUntilTarget === 0) {
      // 同一天，检查时间
      nextDate.setHours(hours, minutes, 0, 0)
      if (nextDate <= currentTime) {
        nextDate.setDate(nextDate.getDate() + 7) // 下周
      }
    } else {
      nextDate.setDate(nextDate.getDate() + daysUntilTarget)
      nextDate.setHours(hours, minutes, 0, 0)
    }
    
    return nextDate.getTime()
  }

  // 获取下一个每日重复日期
  static getNextDailyDate(reminder, currentTime) {
    const [hours, minutes] = reminder.time.split(':').map(Number)
    let nextDate = new Date(currentTime)
    nextDate.setHours(hours, minutes, 0, 0)
    
    // 如果今天的时间已过，移到明天
    if (nextDate <= currentTime) {
      nextDate.setDate(nextDate.getDate() + 1)
    }
    
    return nextDate.getTime()
  }

  // 检查平台是否支持本地通知
  static isPlatformSupported() {
    // #ifdef H5
    return false // H5不支持本地通知
    // #endif
    
    // #ifdef MP
    return false // 小程序通常不支持本地通知
    // #endif
    
    // #ifdef APP-PLUS
    return true // APP支持本地通知
    // #endif
    
    // 默认返回false，使用应用内提醒
    return typeof uni !== 'undefined' && uni.createLocalNotification
  }

  // 创建本地通知
  static createLocalNotification(reminder, triggerTime) {
    try {
      if (typeof uni === 'undefined' || !uni.createLocalNotification) {
        console.warn('uni.createLocalNotification 不可用')
        return false
      }

      uni.createLocalNotification({
        id: reminder.id,
        title: reminder.title,
        content: reminder.content,
        when: triggerTime,
        sound: 'system',
        success: () => {
          console.log('本地通知创建成功:', reminder.title)
        },
        fail: (error) => {
          console.error('本地通知创建失败:', error)
        }
      })

      return true
    } catch (error) {
      console.error('创建本地通知异常:', error)
      return false
    }
  }

  // 安排应用内提醒（降级方案）
  static scheduleInAppReminder(reminder, triggerTime) {
    const delay = triggerTime - Date.now()
    if (delay > 0) {
      setTimeout(() => {
        this.showInAppReminder(reminder)
      }, delay)
    }
  }

  // 显示应用内提醒
  static showInAppReminder(reminder) {
    if (typeof uni !== 'undefined' && uni.showModal) {
      uni.showModal({
        title: reminder.title,
        content: reminder.content,
        showCancel: false,
        confirmText: '知道了'
      })
    }
  }

  // 取消提醒
  static cancelReminder(reminderId) {
    try {
      // 从存储中删除
      const success = StorageUtil.deleteReminder(reminderId)
      
      // 取消本地通知
      if (typeof uni !== 'undefined' && uni.cancelLocalNotification) {
        uni.cancelLocalNotification({
          id: reminderId
        })
      }

      return success
    } catch (error) {
      console.error('取消提醒失败:', error)
      return false
    }
  }

  // 更新提醒
  static updateReminder(reminder) {
    try {
      // 先取消旧的通知
      this.cancelReminder(reminder.id)
      
      // 保存更新后的提醒
      const success = StorageUtil.saveReminder(reminder)
      if (success) {
        // 重新安排通知
        this.scheduleNotification(reminder)
      }
      
      return success
    } catch (error) {
      console.error('更新提醒失败:', error)
      return false
    }
  }

  // 获取所有提醒
  static getAllReminders() {
    return StorageUtil.getReminders()
  }

  // 获取启用的提醒
  static getEnabledReminders() {
    return StorageUtil.getEnabledReminders()
  }

  // 清理过期提醒
  static cleanupExpiredReminders() {
    try {
      const reminders = this.getAllReminders()
      const now = Date.now()
      let cleanedCount = 0

      reminders.forEach(reminder => {
        if (reminder.type === this.REMINDER_TYPES.ONCE) {
          const triggerTime = this.calculateNextTriggerTime(reminder)
          if (triggerTime && triggerTime < now) {
            reminder.status = this.REMINDER_STATUS.EXPIRED
            StorageUtil.saveReminder(reminder)
            cleanedCount++
          }
        }
      })

      console.log(`清理了 ${cleanedCount} 个过期提醒`)
      return cleanedCount
    } catch (error) {
      console.error('清理过期提醒失败:', error)
      return 0
    }
  }

  // 重新安排所有提醒
  static rescheduleAllReminders() {
    try {
      const reminders = this.getEnabledReminders()
      let scheduledCount = 0

      reminders.forEach(reminder => {
        if (this.scheduleNotification(reminder)) {
          scheduledCount++
        }
      })

      console.log(`重新安排了 ${scheduledCount} 个提醒`)
      return scheduledCount
    } catch (error) {
      console.error('重新安排提醒失败:', error)
      return 0
    }
  }
}
