// 本地存储工具类
export class StorageUtil {
  // 保存数据
  static setItem(key, value) {
    try {
      uni.setStorageSync(key, JSON.stringify(value))
    } catch (e) {
      console.error('存储数据失败:', e)
    }
  }

  // 获取数据
  static getItem(key, defaultValue = null) {
    try {
      const value = uni.getStorageSync(key)
      if (!value || value === '') {
        return defaultValue
      }
      return JSON.parse(value)
    } catch (e) {
      console.error('获取数据失败:', e)
      // 在APK环境下，如果存储访问失败，返回默认值而不是抛出异常
      return defaultValue
    }
  }

  // 删除数据
  static removeItem(key) {
    try {
      uni.removeStorageSync(key)
    } catch (e) {
      console.error('删除数据失败:', e)
    }
  }

  // 清空所有数据
  static clear() {
    try {
      uni.clearStorageSync()
    } catch (e) {
      console.error('清空数据失败:', e)
    }
  }

  // 保存记录
  static saveRecord(record) {
    try {
      const records = this.getRecords()
      records.unshift(record) // 新记录添加到开头
      this.setItem('random_records', records)
    } catch (e) {
      console.error('保存记录失败:', e)
      // 静默处理，不影响用户体验
    }
  }

  // 获取所有记录
  static getRecords() {
    try {
      const records = this.getItem('random_records', [])
      // 确保返回的是数组
      return Array.isArray(records) ? records : []
    } catch (e) {
      console.error('获取记录失败:', e)
      return []
    }
  }

  // 根据模式获取记录
  static getRecordsByMode(mode) {
    try {
      const records = this.getRecords()
      return records.filter(record => record && record.mode === mode)
    } catch (e) {
      console.error('获取模式记录失败:', e)
      return []
    }
  }

  // 删除记录
  static deleteRecord(recordId) {
    try {
      const records = this.getRecords()
      const filteredRecords = records.filter(record => record && record.id !== recordId)
      this.setItem('random_records', filteredRecords)
      return true
    } catch (e) {
      console.error('删除记录失败:', e)
      return false
    }
  }

  // 搜索记录
  static searchRecords(keyword, mode = null) {
    try {
      const records = mode ? this.getRecordsByMode(mode) : this.getRecords()
      if (!keyword) return records

      return records.filter(record =>
        record && record.text && record.text.toLowerCase().includes(keyword.toLowerCase())
      )
    } catch (e) {
      console.error('搜索记录失败:', e)
      return []
    }
  }

  // 保存密码
  static setPassword(password) {
    this.setItem('app_password', password)
  }

  // 获取密码
  static getPassword() {
    return this.getItem('app_password', '')
  }

  // 检查是否已登录
  static isLoggedIn() {
    return this.getItem('is_logged_in', false)
  }

  // 设置登录状态
  static setLoginStatus(status) {
    this.setItem('is_logged_in', status)
  }

  // ==================== 历法相关存储方法 ====================

  // 获取历法设置
  static getCalendarSettings() {
    return this.getItem('calendar_settings', {
      showLunar: true,          // 显示农历
      showBuddhist: true,       // 显示佛历
      showTaoist: true,         // 显示道历
      reminderEnabled: true,    // 启用提醒功能
      defaultView: 'calendar',  // 默认视图: calendar, buddhist, taoist
      showFestivals: true,      // 显示节日
      showJieqi: true,          // 显示节气
      showYiJi: true           // 显示宜忌
    })
  }

  // 保存历法设置
  static saveCalendarSettings(settings) {
    try {
      const currentSettings = this.getCalendarSettings()
      const newSettings = { ...currentSettings, ...settings }
      this.setItem('calendar_settings', newSettings)
      return true
    } catch (e) {
      console.error('保存历法设置失败:', e)
      return false
    }
  }

  // 获取所有提醒
  static getReminders() {
    try {
      const reminders = this.getItem('calendar_reminders', [])
      return Array.isArray(reminders) ? reminders : []
    } catch (e) {
      console.error('获取提醒失败:', e)
      return []
    }
  }

  // 保存提醒
  static saveReminder(reminder) {
    try {
      const reminders = this.getReminders()
      const existingIndex = reminders.findIndex(r => r.id === reminder.id)

      if (existingIndex >= 0) {
        reminders[existingIndex] = reminder
      } else {
        reminders.push(reminder)
      }

      this.setItem('calendar_reminders', reminders)
      return true
    } catch (e) {
      console.error('保存提醒失败:', e)
      return false
    }
  }

  // 删除提醒
  static deleteReminder(reminderId) {
    try {
      const reminders = this.getReminders()
      const filteredReminders = reminders.filter(r => r.id !== reminderId)
      this.setItem('calendar_reminders', filteredReminders)
      return true
    } catch (e) {
      console.error('删除提醒失败:', e)
      return false
    }
  }

  // 获取启用的提醒
  static getEnabledReminders() {
    try {
      const reminders = this.getReminders()
      return reminders.filter(r => r.enabled === true)
    } catch (e) {
      console.error('获取启用提醒失败:', e)
      return []
    }
  }

  // 获取历法数据缓存
  static getCachedCalendarData(dateKey) {
    try {
      const cache = this.getItem('calendar_cache', {})
      const cachedData = cache[dateKey]

      if (cachedData && cachedData.timestamp) {
        // 检查缓存是否过期（24小时）
        const now = Date.now()
        const cacheAge = now - cachedData.timestamp
        const maxAge = 24 * 60 * 60 * 1000 // 24小时

        if (cacheAge < maxAge) {
          return cachedData.data
        }
      }

      return null
    } catch (e) {
      console.error('获取历法缓存失败:', e)
      return null
    }
  }

  // 设置历法数据缓存
  static setCachedCalendarData(dateKey, data) {
    try {
      const cache = this.getItem('calendar_cache', {})
      cache[dateKey] = {
        data: data,
        timestamp: Date.now()
      }

      // 清理过期缓存（保留最近30天）
      const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000)
      Object.keys(cache).forEach(key => {
        if (cache[key].timestamp < thirtyDaysAgo) {
          delete cache[key]
        }
      })

      this.setItem('calendar_cache', cache)
      return true
    } catch (e) {
      console.error('设置历法缓存失败:', e)
      return false
    }
  }

  // 清理历法缓存
  static clearCalendarCache() {
    try {
      this.removeItem('calendar_cache')
      return true
    } catch (e) {
      console.error('清理历法缓存失败:', e)
      return false
    }
  }

  // 保存历法查看记录
  static saveCalendarViewRecord(date, viewType = 'calendar') {
    try {
      const records = this.getItem('calendar_view_records', [])
      const record = {
        id: Date.now().toString(36) + Math.random().toString(36).substr(2),
        date: date.toISOString(),
        viewType: viewType,
        timestamp: Date.now()
      }

      records.unshift(record)

      // 只保留最近100条记录
      if (records.length > 100) {
        records.splice(100)
      }

      this.setItem('calendar_view_records', records)
      return true
    } catch (e) {
      console.error('保存历法查看记录失败:', e)
      return false
    }
  }

  // 获取历法查看记录
  static getCalendarViewRecords() {
    try {
      const records = this.getItem('calendar_view_records', [])
      return Array.isArray(records) ? records : []
    } catch (e) {
      console.error('获取历法查看记录失败:', e)
      return []
    }
  }
}
